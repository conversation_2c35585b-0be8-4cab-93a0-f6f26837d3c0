package com.swcares.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.entity.MnjxOffice;
import com.swcares.entity.MnjxTicketLimits;
import com.swcares.obj.vo.OfficeListVo;
import com.swcares.obj.vo.TicketLimitsRetrieveVo;
import com.swcares.obj.vo.TicketLimitsVo;

import java.util.List;

/**
 * description：ITicketLimitsService
 *
 * <AUTHOR>
 * @date 2022/03/30
 */
public interface ITicketLimitsService {
    /**
     * description：新增
     *
     * @param mnjxTicketLimits mnjxTicketLimits
     * @return 新增
     * @throws UnifiedResultException 统一异常
     */
    boolean create(MnjxTicketLimits mnjxTicketLimits) throws UnifiedResultException;

    /***
     * 下拉查询机构
     * @param officeType 机构类型
     * @return 下拉查询机构
     */
    List<OfficeListVo> retrieveOrgDropdownList(String officeType);

    /***
     * 下拉查询OFFICE号
     * @param officeType 机构类型
     * @param orgId 机构主键id
     * @return
     */
    List<MnjxOffice> retrieveOfficeNoDropdownList(String officeType, String orgId);

    /***
     * 分页查询
     * @param page 分页page对象
     * @param ticketLimitsRetrieveVo 分页查询参数
     * @return
     */
    IPage<TicketLimitsVo> retrieveListByPage(IPage<TicketLimitsVo> page, TicketLimitsRetrieveVo ticketLimitsRetrieveVo);

    /**
     * description：更新
     *
     * @param mnjxTicketLimits mnjxTicketLimits
     * @return 更新
     * @throws UnifiedResultException 统一异常
     */
    boolean update(MnjxTicketLimits mnjxTicketLimits) throws UnifiedResultException;

    /**
     * 通过ID删除票号范围
     *
     * @param ticketLimitsId ticketLimitsId
     * @return 通过ID删除票号范围
     */
    boolean deleteById(String ticketLimitsId);
}
