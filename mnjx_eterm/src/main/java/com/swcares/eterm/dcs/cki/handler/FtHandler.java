package com.swcares.eterm.dcs.cki.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.service.IFtService;

import javax.annotation.Resource;

/**
 * FT指令：航班号/日期
 * 设置默认航班
 *
 * <AUTHOR>
 */
@OperateType(action = "FT",shorthand = true)
public class FtHandler implements Handler {

    @Resource
    public IFtService iFtService;

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        // 格式判断
        return iFtService.handle(cmd);
    }
}
