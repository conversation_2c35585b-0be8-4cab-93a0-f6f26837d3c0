package com.swcares.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_luggage_unmanned")
public class MnjxLuggageUnmanned extends Model<MnjxLuggageUnmanned> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "luggage_id", type = IdType.ASSIGN_ID)
    private String luggageId;

    @TableField("plan_section_id")
    private String planSectionId;

    @ApiModelProperty(value = "行李重量")
    @TableField("luggage_weight")
    private BigDecimal luggageWeight;

    @ApiModelProperty(value = "交运时间")
    @TableField("deliver_time")
    private String deliverTime;

    @ApiModelProperty(value = "判读结果：1（通过）,0（不通过）")
    @TableField("deliver_result")
    private String deliverResult;


    @Override
    protected Serializable pkVal() {
        return this.luggageId;
    }

}
