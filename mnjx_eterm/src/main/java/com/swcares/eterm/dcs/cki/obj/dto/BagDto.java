package com.swcares.eterm.dcs.cki.obj.dto;

import com.swcares.core.util.PinyinUtils;
import com.swcares.core.util.StrUtils;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BagDto {

    private String order;
    private String multiOrder;

    private List<String> bagNoList;

    /**
     * 旅客姓名
     */
    private String psgName;

    /**
     * 行李件数
     */
    private String bagNum;

    /**
     * 行李总重量
     */
    private String bagWeight;

    /**
     * PNR号
     */
    private String crsPnr;

    /**
     * 舱位等级
     */
    private String cabinClass;

    /**
     * 行李牌号(航空公司用两字代码表示)
     */
    private String tagNumberBarCode;

    private String tagNumberBarCodeLast;

    /**
     * 行李牌号(航空公司用数字编号表示)
     */
    private String claimTagBarCode;

    /**
     * 航班数量
     */
    private String fltNum;

    /**
     * 航班号
     */
    private String fltNo;

    /**
     * 航班日期
     */
    private String fltDate;

    /**
     * 出发地
     */
    private String org;

    /**
     * 到达地
     */
    private String dst;

    /**
     * 旅客登机号
     */
    private String paxBoardingNum;

    /**
     * 办理行李手续的AGENT号
     */
    private String agentNum;

    private List<BagPrintDto> bagList;

    /**
     * Title：printBag <br>
     * description：行李牌打印数据 <br>
     *
     * @return <br>
     * <AUTHOR> <br>
     * @date 2020/11/09 <br>
     */
    public String printBag() {
        StringBuffer sb = new StringBuffer();
        String bagHeader = "BTP010101";
        // 行李牌头（版本）
        sb.append(bagHeader);
        if (StrUtils.strNotEmpty(psgName)) {
            // 旅客姓名
            sb.append("#01").append(PinyinUtils.getPinYin(psgName, false));
        }
        if (StrUtils.strNotEmpty(bagNum)) {
            //行李件数
            sb.append("#02").append(bagNum);
        }
        if (StrUtils.strNotEmpty(bagWeight)) {
            //行李总重量
            sb.append("#03").append(bagWeight);
        }
        if (StrUtils.strNotEmpty(crsPnr)) {
            //PNR号
            sb.append("#04").append(crsPnr);
        }
        if (StrUtils.strNotEmpty(cabinClass)) {
            //舱位等级
            sb.append("#05").append(cabinClass);
        }
        if (StrUtils.strNotEmpty(tagNumberBarCode) && StrUtils.strNotEmpty(tagNumberBarCodeLast)) {
            //行李牌号(航空公司用两字代码表示)
            sb.append("#06").append(tagNumberBarCode).append(" ").append(tagNumberBarCodeLast);
        }
        if (StrUtils.strNotEmpty(fltNum)) {
            //航班数量
            sb.append("#07").append(fltNum);
        }
        if (StrUtils.strNotEmpty(org)) {
            //起飞城市
            sb.append("#08").append(org);
        }
        if (StrUtils.strNotEmpty(claimTagBarCode)) {
            //行李牌号(航空公司用数字编号表示)
            sb.append("#09").append(claimTagBarCode);
        }
        if (StrUtils.strNotEmpty(paxBoardingNum)) {
            //旅客登机号
            sb.append("#0A").append(paxBoardingNum);
        }
        if (StrUtils.strNotEmpty(agentNum)) {
            //办理行李手续的AGENT号
            sb.append("#0B").append(agentNum);
        }
        // 所有航段
        allLegs(sb);
        // 结尾符
        sb.append("#");
        return sb.toString();
    }

    /**
     * Title：allLegs <br>
     * description：所有航段数据 <br>
     *
     * @param sb <br>
     * <AUTHOR> <br>
     * @date 2020/11/09 <br>
     */
    private void allLegs(StringBuffer sb) {
        for (BagPrintDto bagPrintDto : bagList) {
            String secondAirlineCode = bagPrintDto.getSecondAirlineCode();
            if (StrUtils.strNotEmpty(secondAirlineCode)) {
                if (StrUtils.strNotEmpty(bagPrintDto.getAirlineCode())) {
                    //航班两字代码   （倒数第一段航段）
                    sb.append(String.format("#%s0", secondAirlineCode)).append(bagPrintDto.getAirlineCode());
                }
                if (StrUtils.strNotEmpty(bagPrintDto.getFltNo())) {
                    //航班号   （倒数第一段航段）
                    sb.append(String.format("#%s1", secondAirlineCode)).append(bagPrintDto.getFltNo().substring(2));
                }
                if (StrUtils.strNotEmpty(bagPrintDto.getFltDate())) {
                    //起飞日期   （倒数第一段航段）
                    sb.append(String.format("#%s2", secondAirlineCode)).append(bagPrintDto.getFltDate(), 0, bagPrintDto.getFltDate().length() - 2);
                }
                if (StrUtils.strNotEmpty(bagPrintDto.getDst())) {
                    //目的城市三字代码   （倒数第一段航段）
                    sb.append(String.format("#%s5", secondAirlineCode)).append(bagPrintDto.getDst());
                }
                if (StrUtils.strNotEmpty(bagPrintDto.getDstName())) {
                    //目的城市名称   （倒数第一段航段）
                    sb.append(String.format("#%s6", secondAirlineCode)).append(bagPrintDto.getDstName());
                }
            }
        }
    }
}
