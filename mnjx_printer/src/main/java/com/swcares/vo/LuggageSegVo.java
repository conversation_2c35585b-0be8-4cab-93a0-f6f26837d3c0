package com.swcares.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 行李牌航段
 *
 * <AUTHOR>
 */
@ApiModel(value = "luggageSegVo", description = "行李牌航段打印规范")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class LuggageSegVo {
    /**
     * 序号
     */
    @ApiModelProperty("序号")
    private String serialNo;
    /**
     * 航班号
     */
    @ApiModelProperty("航班号")
    private String flightNo;
    /**
     * 出发城市3字码
     */
    @ApiModelProperty("城市3字码")
    private String org;
    /**
     * 出发城市中文
     */
    @ApiModelProperty("城市中文")
    private String orgZh;
    /**
     * 目的城市3字码
     */
    @ApiModelProperty("目的城市3字码")
    private String dst;
    /**
     * 目的城市中文
     */
    @ApiModelProperty("目的城市中文")
    private String dstZh;
    /**
     * 航班日期
     */
    @ApiModelProperty("航班日期")
    private String flightDate;
}
