package com.swcares.obj.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/10 14:16
 */
@Data
@ApiModel(value = "SkVo", description = "SkVo")
public class SkVo implements Serializable {

    @ApiModelProperty(value = "起始日期", example = "2025-04-20T00:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm")
    private LocalDateTime originData;

    @ApiModelProperty(value = "结束日期", example = "2025-04-26T00:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm")
    private LocalDateTime endData;

    @ApiModelProperty("航程信息列表")
    private List<Voyage> voyage = new ArrayList<>();

    @Data
    @ApiModel("航程详细信息")
    public static class Voyage implements Serializable {
        @ApiModelProperty("主键ID")
        private Long pkId;

        @ApiModelProperty(value = "经停城市标识", example = "0")
        private String stopCity;

        @ApiModelProperty("总耗时")
        private String totalTime;

        @ApiModelProperty("中转城市列表")
        private List<String> transitCities = new ArrayList<>();

        @ApiModelProperty("航段列表")
        private List<Segment> segments = new ArrayList<>();
    }

    @Data
    @ApiModel("航班航段信息")
    public static class Segment implements Serializable {
        @ApiModelProperty("到达机场中文名")
        private String arrivalAirportCN;

        @ApiModelProperty(value = "出发机场代码", example = "SHA")
        private String departureAirportCode;

        @ApiModelProperty("出发机场中文名")
        private String departureAirportCN;

        @ApiModelProperty(value = "到达时间", example = "09:15")
        private String arrivalTime;

        @ApiModelProperty(value = "出发时间", example = "07:00")
        private String departureTime;

        @ApiModelProperty(value = "到达机场代码", example = "PEK")
        private String arrivalAirportCode;

        @ApiModelProperty("飞行时间（分钟）")
        private Integer flightTime;

        @ApiModelProperty("飞行距离（公里）")
        private Integer flightDistance;

        @ApiModelProperty(value = "航段天数", example = "0")
        private String segDays;

        @ApiModelProperty("出发天数")
        private String depDays;

        @ApiModelProperty("到达天数")
        private String arrDays;

        @ApiModelProperty(value = "ASR标识", example = "^")
        private String asr;

        @ApiModelProperty("衔接等级")
        private String connectLevel;

        @ApiModelProperty("航空公司信息")
        private Airlines airlines;

        @ApiModelProperty("舱位列表")
        private List<Cabin> cabins = new ArrayList<>();

        @ApiModelProperty(value = "经停次数", example = "0")
        private Integer stopNum;

        @ApiModelProperty("")
        private String lnk;

        @ApiModelProperty("餐食")
        private String commonMeal;

        @ApiModelProperty("娱乐标识")
        private Boolean et;
    }

    @Data
    @ApiModel("航空公司信息")
    public static class Airlines implements Serializable {
        @ApiModelProperty(value = "航空公司代码", example = "MU")
        private String airCode;

        @ApiModelProperty("航空公司中文名")
        private String airCN;

        @ApiModelProperty(value = "航班号", example = "5099")
        private String flightNo;

        @ApiModelProperty(value = "机型", example = "333")
        private String planeType;

        @ApiModelProperty("是否共享航班")
        private Boolean isShared;

        @ApiModelProperty("联盟信息")
        private String alliance;

        @ApiModelProperty("航空服务等级")
        private String airService;

        @ApiModelProperty(value = "班期", example = "每天飞")
        private String schedule;

        @ApiModelProperty(value = "起始运营日期", example = "2025-03-30")
        private String departureDate;

        @ApiModelProperty(value = "结束运营日期", example = "2025-10-25")
        private String arrivalDate;
    }

    @Data
    @ApiModel("舱位信息")
    public static class Cabin implements Serializable {
        @ApiModelProperty(value = "舱位等级", example = "J")
        private String cabinName;

        @ApiModelProperty("舱位状态")
        private String state;
    }
}
