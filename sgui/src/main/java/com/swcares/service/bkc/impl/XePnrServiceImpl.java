package com.swcares.service.bkc.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.security.custom.UserInfo;
import com.swcares.entity.*;
import com.swcares.obj.dto.XePnrDto;
import com.swcares.service.*;
import com.swcares.service.bkc.IXePnrService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 取消PNR服务实现类
 *
 * <AUTHOR>
 * @date 2025/1/2 10:30
 */
@Service
public class XePnrServiceImpl implements IXePnrService {

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxPnrRecordService iMnjxPnrRecordService;

    @Resource
    private IMnjxPnrAtService iMnjxPnrAtService;

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;

    @Resource
    private IMnjxOpenCabinService iMnjxOpenCabinService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String xePnr(XePnrDto dto) throws SguiResultException {
        // 参数校验
        if (StrUtil.isEmpty(dto.getPnrNo())) {
            throw new SguiResultException("PNR编号不能为空");
        }

        // 通过pnrNo查询PNR
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, dto.getPnrNo())
                .one();

        if (pnr == null) {
            throw new SguiResultException("PNR不存在");
        }

        // 检查PNR状态
        if ("DEL".equals(pnr.getPnrStatus())) {
            throw new SguiResultException("该PNR已取消");
        }

        // 将PNR状态修改为DEL
        pnr.setPnrStatus("DEL");
        iMnjxPnrService.updateById(pnr);

        // 更新行动代码为XX
        List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .isNotNull(MnjxPnrSeg::getActionCode)
                .list();
        pnrSegList.forEach(p -> {
            p.setActionCode("XX");
            String inputValue = p.getInputValue();
            inputValue = inputValue.replace(" RR", " XX");
            inputValue = inputValue.replace(" HK", " XX");
            inputValue = inputValue.replace(" DK", " XX");
            p.setInputValue(inputValue);
        });

        List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .list();
        List<String> pnrNmIdList = pnrNmList.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList());
        List<MnjxNmSsr> ssrList = iMnjxNmSsrService.lambdaQuery()
                .in(MnjxNmSsr::getPnrNmId, pnrNmIdList)
                .isNotNull(MnjxNmSsr::getActionCode)
                .list();
        ssrList.forEach(p -> {
            p.setActionCode("XX");
            String inputValue = p.getInputValue();
            inputValue = inputValue.replace(" RR", " XX");
            inputValue = inputValue.replace(" HK", " XX");
            inputValue = inputValue.replace(" DK", " XX");
            p.setInputValue(inputValue);
            p.setSsrInfo(inputValue);
        });
        iMnjxPnrSegService.updateBatchById(pnrSegList);
        iMnjxNmSsrService.updateBatchById(ssrList);

        // 恢复消耗的座位数
        List<MnjxPnrSeg> pnrSegListForSeat = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .ne(MnjxPnrSeg::getPnrSegType, "SA") // 排除地面段
                .isNotNull(MnjxPnrSeg::getSeatNumber)
                .list();

        for (MnjxPnrSeg pnrSeg : pnrSegListForSeat) {
            // 获取该航段的开舱信息
            List<MnjxOpenCabin> openCabinList = iSguiCommonService.getOpenCabinListByFlightNo(
                    pnrSeg.getFlightNo(),
                    pnrSeg.getFlightDate(),
                    pnrSeg.getOrg(),
                    pnrSeg.getDst()
            );

            // 恢复对应舱位的座位数
            openCabinList.stream()
                    .filter(o -> o.getSellCabin().equals(pnrSeg.getSellCabin()))
                    .forEach(k -> {
                        int availableNumber = k.getSeatAvailable() + pnrSeg.getSeatNumber();
                        k.setSeatAvailable(availableNumber);
                    });

            // 更新开舱数据
            if (CollUtil.isNotEmpty(openCabinList)) {
                iMnjxOpenCabinService.updateBatchById(openCabinList);
            }
        }
        

        // 生成新的封口记录
        String newAtNo = iSguiCommonService.generateNewAtNo(pnr.getPnrId());
        MnjxPnrAt pnrAt = new MnjxPnrAt();
        pnrAt.setPnrAtId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrAt.setPnrId(pnr.getPnrId());
        pnrAt.setAtNo(newAtNo);
        MnjxPnrSeg seg = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByDesc(MnjxPnrSeg::getPnrSegNo)
                .list()
                .get(0);
        pnrAt.setAtType(seg.getFlightNo().substring(0, 2));

        // 获取当前用户信息
        UserInfo userInfo = iSguiCommonService.getCurrentUserInfo();
        if (userInfo != null) {
            pnrAt.setAtSiId(userInfo.getSiId());
        }

        pnrAt.setAtDateTime(new Date());

        // 保存封口记录
        iMnjxPnrAtService.save(pnrAt);

        // 更新所有changeMark为空的pnrRecord记录
        List<MnjxPnrRecord> recordsToUpdate = iMnjxPnrRecordService.lambdaQuery()
                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                .and(wrapper -> wrapper.isNull(MnjxPnrRecord::getChangeMark)
                        .or()
                        .eq(MnjxPnrRecord::getChangeMark, ""))
                .list();

        if (CollUtil.isNotEmpty(recordsToUpdate)) {
            for (MnjxPnrRecord record : recordsToUpdate) {
                record.setChangeMark("D");
                record.setChangeAtNo(newAtNo);
                if (StrUtil.equalsAny(record.getPnrType(), "SEG", "SSR")) {
                    String inputValue = record.getInputValue();
                    inputValue = inputValue.replace(" RR", " XX");
                    inputValue = inputValue.replace(" HK", " XX");
                    inputValue = inputValue.replace(" KK", " XX");
                    record.setInputValue(inputValue);
                }
            }
            iMnjxPnrRecordService.updateBatchById(recordsToUpdate);
        }

        return dto.getPnrNo();
    }
}
