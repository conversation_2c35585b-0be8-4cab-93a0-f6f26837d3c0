package com.swcares.eterm.dcs.cki.mapper;

import com.swcares.entity.*;
import com.swcares.eterm.dcs.cki.obj.dto.FlightSectionDto;
import com.swcares.eterm.dcs.cki.obj.dto.PrCtcDto;
import com.swcares.eterm.dcs.cki.obj.dto.PrDto;
import com.swcares.eterm.dcs.cki.obj.dto.PsgCityDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface PrMapper {

    /**
     * 查询航班是否被初始化
     *
     * @param flightNo   flightNo
     * @param flightDate flightDate
     * @return 查询航班是否被初始化
     */
    MnjxPlanFlight retrievePlanFlightInitial(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate);

    /**
     * 查询航班舱位
     *
     * @param flightNo   flightNo
     * @param flightDate flightDate
     * @param sellCabin  sellCabin
     * @param aptId      aptId
     * @return 查询航班舱位
     */
    MnjxOpenCabin retrieveSellCabin(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate, @Param("sellCabin") String sellCabin,
                                    @Param("aptId") String aptId);

    /**
     * 查询航站的出发城市
     *
     * @param flightNo  flightNo
     * @param airportId airportId
     * @return 查询航站的出发城市
     */
    MnjxTcardSection retrieveDepartureCity(@Param("flightNo") String flightNo, @Param("airportId") String airportId);

    /**
     * 查询航班座位
     *
     * @param flightNo   flightNo
     * @param flightDate flightDate
     * @param seatNo     seatNo
     * @param airportId  airportId
     * @return 查询航班座位
     */
    MnjxSeat retrieveBySeatNo(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate, @Param("seatNo") String seatNo, @Param("airportId") String airportId);

    /**
     * 按姓名模糊查询
     *
     * @param flightNo   flightNo
     * @param flightDate flightDate
     * @param sellCabin  sellCabin
     * @param org        org
     * @param queryName  queryName
     * @return 按姓名模糊查询
     */
    List<PrDto> retrieveByQueryName(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate, @Param("sellCabin") String sellCabin,
                                    @Param("org") String org, @Param("queryName") String queryName);

    /**
     * retrieveByQueryNameAndCarrierFlight
     *
     * @param flightNo   flightNo
     * @param flightDate flightDate
     * @param sellCabin  sellCabin
     * @param org        org
     * @param queryName  queryName
     * @return retrieveByQueryNameAndCarrierFlight
     */
    List<PrDto> retrieveByQueryNameAndCarrierFlight(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate, @Param("sellCabin") String sellCabin,
                                                    @Param("org") String org, @Param("queryName") String queryName);

    /**
     * 通过 CSR编码、ICS编码查询
     *
     * @param flightNo   flightNo
     * @param flightDate flightDate
     * @param sellCabin  sellCabin
     * @param org        org
     * @param pnr        pnr
     * @return 通过 CSR编码、ICS编码查询
     */
    List<PrDto> retrieveByPnr(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate, @Param("sellCabin") String sellCabin,
                              @Param("org") String org, @Param("pnr") String pnr);


    /**
     * retrieveByPnrAndCarrierFlight
     *
     * @param flightNo   flightNo
     * @param flightDate flightDate
     * @param sellCabin  sellCabin
     * @param org        org
     * @param pnr        pnr
     * @return retrieveByPnrAndCarrierFlight
     */
    List<PrDto> retrieveByPnrAndCarrierFlight(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate, @Param("sellCabin") String sellCabin,
                                              @Param("org") String org, @Param("pnr") String pnr);

    /**
     * 按座位号查询旅客信息
     *
     * @param flightNo   flightNo
     * @param flightDate flightDate
     * @param org        org
     * @param seatNo     seatNo
     * @param sellCabin  sellCabin
     * @return 按座位号查询旅客信息
     */
    List<PrDto> retrieveBySeatNoPr(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate, @Param("sellCabin") String sellCabin,
                                   @Param("org") String org, @Param("seatNo") String seatNo);

    /**
     * retrieveBySeatNoPrAndCarrierFlight
     *
     * @param flightNo   flightNo
     * @param flightDate flightDate
     * @param sellCabin  sellCabin
     * @param org        org
     * @param seatNo     seatNo
     * @return retrieveBySeatNoPrAndCarrierFlight
     */
    List<PrDto> retrieveBySeatNoPrAndCarrierFlight(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate, @Param("sellCabin") String sellCabin,
                                                   @Param("org") String org, @Param("seatNo") String seatNo);


    /**
     * 根据登机口查询旅客信息
     *
     * @param flightNo   flightNo
     * @param flightDate flightDate
     * @param org        org
     * @param aboardNo   aboardNo
     * @param sellCabin  sellCabin
     * @return 根据登机口查询旅客信息
     */
    List<PrDto> retrieveByAboardNo(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate, @Param("sellCabin") String sellCabin,
                                   @Param("org") String org, @Param("aboardNo") String aboardNo);

    /**
     * retrieveByAboardNoAndCarrierFlight
     *
     * @param flightNo   flightNo
     * @param flightDate flightDate
     * @param sellCabin  sellCabin
     * @param org        org
     * @param aboardNo   aboardNo
     * @return retrieveByAboardNoAndCarrierFlight
     */
    List<PrDto> retrieveByAboardNoAndCarrierFlight(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate, @Param("sellCabin") String sellCabin,
                                                   @Param("org") String org, @Param("aboardNo") String aboardNo);


    /**
     * 通过票号查询
     *
     * @param flightNo   flightNo
     * @param flightDate flightDate
     * @param sellCabin  sellCabin
     * @param org        org
     * @param ticketNo   ticketNo
     * @return 通过票号查询
     */
    List<PrDto> retrieveByTicketNo(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate, @Param("sellCabin") String sellCabin,
                                   @Param("org") String org, @Param("ticketNo") String ticketNo);

    /**
     * retrieveByTicketNoAndCarrierFlight
     *
     * @param flightNo   flightNo
     * @param flightDate flightDate
     * @param sellCabin  sellCabin
     * @param org        org
     * @param ticketNo   ticketNo
     * @return retrieveByTicketNoAndCarrierFlight
     */
    List<PrDto> retrieveByTicketNoAndCarrierFlight(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate, @Param("sellCabin") String sellCabin,
                                                   @Param("org") String org, @Param("ticketNo") String ticketNo);

    /**
     * 通过行李编号
     *
     * @param flightNo   flightNo
     * @param flightDate flightDate
     * @param sellCabin  sellCabin
     * @param org        org
     * @param luggageNo  luggageNo
     * @return 通过行李编号
     */
    List<PrDto> retrieveByLuggageNo(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate, @Param("sellCabin") String sellCabin,
                                    @Param("org") String org, @Param("luggageNo") String luggageNo);

    /**
     * retrieveByLuggageNoAndCarrierFlight
     *
     * @param flightNo   flightNo
     * @param flightDate flightDate
     * @param sellCabin  sellCabin
     * @param org        org
     * @param luggageNo  luggageNo
     * @return retrieveByLuggageNoAndCarrierFlight
     */
    List<PrDto> retrieveByLuggageNoAndCarrierFlight(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate, @Param("sellCabin") String sellCabin,
                                                    @Param("org") String org, @Param("luggageNo") String luggageNo);

    /**
     * 通过pnrNmId
     *
     * @param pnrNmId     pnrNmId
     * @param flightNo    flightNo
     * @param flightDate  flightDate
     * @param sellCabin   sellCabin
     * @param estimateOff estimateOff
     * @param org         org
     * @return 通过pnrNmId
     */
    List<PrDto> retrieveByPnrNmId(@Param("pnrNmId") String pnrNmId, @Param("flightNo") String flightNo, @Param("flightDate") String flightDate,
                                  @Param("sellCabin") String sellCabin, @Param("estimateOff") String estimateOff, @Param("org") String org);


    /**
     * retrieveByPnrNmIdAndCarrierFlight
     *
     * @param pnrNmId     pnrNmId
     * @param flightNo    flightNo
     * @param flightDate  flightDate
     * @param sellCabin   sellCabin
     * @param estimateOff estimateOff
     * @param org         org
     * @return retrieveByPnrNmIdAndCarrierFlight
     */
    List<PrDto> retrieveByPnrNmIdAndCarrierFlight(@Param("pnrNmId") String pnrNmId, @Param("flightNo") String flightNo, @Param("flightDate") String flightDate,
                                                  @Param("sellCabin") String sellCabin, @Param("estimateOff") String estimateOff, @Param("org") String org);

    /**
     * 查询候补旅客
     *
     * @param hbNo      hbNo
     * @param rangeDate rangeDate
     * @return 查询候补旅客
     */
    List<PrDto> retrieveByPnrHbNo(@Param("hbNo") String hbNo, @Param("rangeDate") List<String> rangeDate);


    /**
     * 根据机场三字码查询城市信息
     *
     * @param airportCode airportCode
     * @return 根据机场三字码查询城市信息
     */
    MnjxCity retrieveByCity(@Param("airportCode") String airportCode);

    /**
     * 查询CND
     *
     * @param flightNo   flightNo
     * @param flightDate flightDate
     * @return 查询CND
     */
    MnjxCnd retrieveByCnd(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate);


    /**
     * 查询出票信息
     *
     * @param pnrNmId pnrNmId
     * @return 查询出票信息
     */
    PrCtcDto retrieveCtcByNmId(@Param("pnrNmId") String pnrNmId);

    /**
     * 通过pnrId查询处理航段对应的城市
     *
     * @param pnrId    pnrId
     * @param pnrSegNo pnrSegNo
     * @return 通过pnrId查询处理航段对应的城市
     */
    List<PsgCityDto> retrieveByPsgCity(@Param("pnrId") String pnrId, @Param("pnrSegNo") String pnrSegNo);

    /**
     * 按行李号查询最近两天内
     *
     * @param luggageNo luggageNo
     * @param rangeDate rangeDate
     * @return 按行李号查询最近两天内
     */
    List<MnjxLuggage> retrieveByBtLuggageNo(@Param("luggageNo") String luggageNo, @Param("rangeDate") List<String> rangeDate);

    /**
     * retrieveBySegTicketNo
     *
     * @param pnrNmId    pnrNmId
     * @param flightNo   flightNo
     * @param flightDate flightDate
     * @return retrieveBySegTicketNo
     */
    List<MnjxPnrNmTicket> retrieveBySegTicketNo(@Param("pnrNmId") String pnrNmId, @Param("flightNo") String flightNo, @Param("flightDate") String flightDate);

    /**
     * 查询联程的下一段、上一段状态
     *
     * @param pnrId   pnrId
     * @param pnrNmId pnrNmId
     * @return 查询联程的下一段、上一段状态
     */
    List<PrDto> retrieveByInterline(@Param("pnrId") String pnrId, @Param("pnrNmId") String pnrNmId);

    /**
     * retrieveByFlightSection
     *
     * @param flightNos   flightNos
     * @param flightDates flightDates
     * @return retrieveByFlightSection
     */
    List<FlightSectionDto> retrieveByFlightSection(@Param("flightNos") List<String> flightNos, @Param("flightDates") List<String> flightDates);

    List<MnjxPlanSection> retrievePlanSection(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate);
}
