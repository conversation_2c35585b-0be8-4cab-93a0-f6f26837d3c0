package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 按票号查询客票详情响应VO
 *
 * <AUTHOR>
 * @date 2025/5/30 14:00
 */
@Data
@ApiModel(value = "按票号查询客票详情响应VO")
public class QueryTicketDetailVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "电子客票号")
    private String etNumber;

    @ApiModelProperty(value = "旅客姓名")
    private String passengerName;

    @ApiModelProperty(value = "旅客姓名后缀")
    private String passengerNameSuffix;

    @ApiModelProperty(value = "姓名后缀")
    private String nameSuffix;

    @ApiModelProperty(value = "旅客类型")
    private String passengerType;

    @ApiModelProperty(value = "票面旅客类型")
    private String ticketPsgType;

    @ApiModelProperty(value = "票类型代码")
    private String ticketTypeCode;

    @ApiModelProperty(value = "特殊旅客类型")
    private String specialPassengerType;

    @ApiModelProperty(value = "全名")
    private String fullName;

    @ApiModelProperty(value = "是否CDS票")
    private Boolean cdsTicket;

    @ApiModelProperty(value = "电子票类型")
    private String etType;

    @ApiModelProperty(value = "是否政府采购")
    private Boolean governmentPurchase;

    @ApiModelProperty(value = "是否已打印行程单")
    private Boolean receiptPrinted;

    @ApiModelProperty(value = "连续票号")
    private String conjunctiveTicket;

    @ApiModelProperty(value = "出票日期")
    private String issueTicketDate;

    @ApiModelProperty(value = "出票时间")
    private String issueTime;

    @ApiModelProperty(value = "协议运价代码")
    private String negotiatedFareCode;

    @ApiModelProperty(value = "是否查询专属协议运价")
    private Boolean queryExclusiveNegotiatedFare;

    @ApiModelProperty(value = "二次验证因素")
    private SecondFactorVo secondFactor;

    @ApiModelProperty(value = "航段信息")
    private List<AirSegVo> airSeg = new ArrayList<>();

    @ApiModelProperty(value = "CRS PNR编号")
    private String airSegCrsPnr;

    /**
     * 二次验证因素VO
     */
    @Data
    @ApiModel(value = "二次验证因素VO")
    public static class SecondFactorVo implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "二次验证因素代码")
        private String secondFactorCode;

        @ApiModelProperty(value = "二次验证因素值")
        private String secondFactorValue;
    }

    /**
     * 航段信息VO
     */
    @Data
    @ApiModel(value = "航段信息VO")
    public static class AirSegVo implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "出发机场代码")
        private String depAirportCode;

        @ApiModelProperty(value = "CRS PNR编号")
        private String crsPnrNo;

        @ApiModelProperty(value = "舱位")
        private String cabin;

        @ApiModelProperty(value = "航段状态")
        private String segmentStatus;

        @ApiModelProperty(value = "航班号")
        private String flightNo;

        @ApiModelProperty(value = "出发时间")
        private String depTime;

        @ApiModelProperty(value = "出发机场航站楼")
        private String depAirportTerminal;

        @ApiModelProperty(value = "到达时间")
        private String arrTime;

        @ApiModelProperty(value = "到达机场代码")
        private String arrAirportCode;

        @ApiModelProperty(value = "票状态")
        private String ticketStatus;

        @ApiModelProperty(value = "到达机场航站楼")
        private String arrAirportTerminal;

        @ApiModelProperty(value = "PNR编号")
        private String pnrNo;

        @ApiModelProperty(value = "费率")
        private String rate;

        @ApiModelProperty(value = "CRS类型")
        private String crsType;

        @ApiModelProperty(value = "经停类型")
        private String stopType;

        @ApiModelProperty(value = "实际承运航司")
        private String operationAirline;

        @ApiModelProperty(value = "航司")
        private String airline;

        @ApiModelProperty(value = "唯一索引")
        private String uniqueIndex;

        @ApiModelProperty(value = "出发日期")
        private String departureDate;

        @ApiModelProperty(value = "变更原因")
        private String changeReason;

        @ApiModelProperty(value = "航段序号")
        private Integer segmentIndex;

        @ApiModelProperty(value = "是否ARNK航段")
        private Boolean segANRK;

        @ApiModelProperty(value = "是否OPEN航段")
        private Boolean segOPEN;
    }
}
