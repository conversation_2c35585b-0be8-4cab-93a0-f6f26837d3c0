package com.swcares.utils;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.NumberUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
public class ArrayUtils extends ArrayUtil {
    /**
     * 找到header开头和tail结尾的中间值的索引
     * eg:
     * byte[] source = new byte[]{1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0};
     * byte[] header = new byte[]{4, 5};
     * byte[] tail = new byte[]{0, 1};
     *
     * @param sources 原始字节码
     * @param headers 开始字节码子数组
     * @param tail    结尾字节码子数组
     * @return 找到header开头和tail结尾的中间值的索引
     */
    public static List<List<Integer>> findIndexOf(byte[] sources, byte[] headers, byte[] tail) {
        // 获得所有的开始坐标索引
        List<Integer> headerIndex = ArrayUtils.indexOfArray(sources, headers, sources.length);
        // 获得所有结束的坐标索引
        List<Integer> tailIndex = ArrayUtils.indexOfArray(sources, tail, sources.length);
        // 取最小的，最小的能成对
        int min = NumberUtil.min(headerIndex.size(), tailIndex.size());
        // 获得开始元素的个数(取坐标的偏移量)
        int offset = headers.length;
        List<List<Integer>> coordinates = new ArrayList<>(min);
        for (int i = 0; i < min; i++) {
            // 开始坐标值
            int startIndex = headerIndex.get(i) + offset;
            int endIndex = tailIndex.get(i);
            List<Integer> coordinate = Stream.iterate(startIndex, step -> step = step + 1).limit(endIndex - startIndex).collect(Collectors.toList());
            coordinates.add(coordinate);
        }
        return coordinates;
    }

    /**
     * 在source中从start位置开始查找sub到end位置结束, 返回sub在source的起始位置, -1表示查找失败
     *
     * @param source 要被查找的数组
     * @param sub    匹配的数组
     * @param end    结束位置
     * @return 在source中从start位置开始查找sub到end位置结束, 返回sub在source的起始位置, -1表示查找失败
     */
    public static List<Integer> indexOfArray(byte[] source, byte[] sub, int end) {
        List<Integer> indexes = new ArrayList<>();
        int sourceIndex = 0;
        int subIndex = 0;
        if (sub != null) {
            while (sourceIndex < source.length && sourceIndex < end) {
                int site = 0;
                // 找到第一个想等的元素
                while (source[sourceIndex + site] == sub[subIndex + site]) {
                    if (subIndex + site + 1 >= sub.length) {
                        indexes.add(sourceIndex);
                    }
                    site++;
                    if (sourceIndex + site >= source.length || subIndex + site >= sub.length) {
                        break;
                    }
                }
                sourceIndex++;
            }
        }
        return indexes;
    }

    /**
     * 将 int 按照小端法映射到 byte[] 中。即最低 8 位放在 byte[0] 中，依次类推.
     * 先保存最低的 8 位到 byte 数组中，然后不断的右移 8 位，每次保存低 8 位数据即可
     *
     * @param arg 整数(int)
     * @return 字节数组
     */
    public static byte[] intToBytes(int arg) {
        // 这里为什么是4，因为int在java中是4个字节
        byte[] ans = new byte[4];
        for (int i = 0; i < 4; i++) {
            //截断 int 的低 8 位为一个字节 byte，并存储起来
            ans[i] = (byte) (arg >> (i * 8));
        }
        return ans;
    }

    /**
     * 将 int 按位从左到右打印
     *
     * @param a
     */
    public static void intPrint(int a) {
        int count = 0;
        for (int i = 31; i >= 0; i--) {
            System.out.print((a >> i) & 1);
            count++;
            //每四位为一组，用空格分开
            if (count == 4) {
                System.out.print(" ");
                count = 0;
            }
        }
        System.out.println();
    }

    public static int bytesToInt(byte[] a) {
        int ans = 0;
        for (int i = 0; i < 4; i++) {
            //左移 8 位
            ans <<= 8;
            //保存 byte 值到 ans 的最低 8 位上
            ans |= a[3 - i];
            intPrint(ans);
        }
        return ans;
    }

    /**
     * 将 byte 按位从左到右打印
     *
     * @param a
     */
    public static void bytePrint(byte a) {
        int count = 0;
        for (int i = 7; i >= 0; i--) {
            System.out.print((a >> i) & 1);
            count++;
            //每四位为一组，用空格分开
            if (count == 4) {
                System.out.print(" ");
                count = 0;
            }
        }
        System.out.println();
    }
}
