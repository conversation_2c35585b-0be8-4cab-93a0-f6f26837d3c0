package com.swcares.core.protocol.btp;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 */
public class BtpDataHeader {
    /**
     * PECTAB 数据流头标识
     */
    private final static String BTP = "BTP";
    /**
     * PECTAB 表号
     */
    private final static String TABLE_NO = "01";
    /**
     * PECTAB 版本号
     */
    private final static String VERSION = "01";
    /**
     * 打印 TAG 数量
     */
    private final static String TAG_NUM = "01";

    public static String toStr() {
        return StrUtil.format("{}{}{}{}", BTP, TABLE_NO, VERSION, TAG_NUM);
    }
}
