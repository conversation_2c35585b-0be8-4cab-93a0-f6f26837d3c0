package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 自动退票响应VO
 *
 * <AUTHOR>
 * @date 2025/1/2 16:00
 */
@Data
@ApiModel(value = "自动退票响应VO")
public class BatchAutoRefundVo {

    @ApiModelProperty(value = "需要座位释放")
    private Boolean needSeatVacated;

    @ApiModelProperty(value = "退票结果列表")
    private List<RefundResult> refundResult;

    @ApiModelProperty(value = "全部成功")
    private Boolean allSuccess;

    @ApiModelProperty(value = "全部失败")
    private Boolean allFail;

    @ApiModelProperty(value = "退票订单")
    private Object refundOrder;

    /**
     * 退票结果
     */
    @Data
    @ApiModel(value = "退票结果")
    public static class RefundResult {
        @ApiModelProperty(value = "票号")
        private String ticketNo;

        @ApiModelProperty(value = "成功标识")
        private Boolean success;

        @ApiModelProperty(value = "金额")
        private Amount amount;

        @ApiModelProperty(value = "退票单号")
        private String trfdno;

        @ApiModelProperty(value = "打印号")
        private String printNo;

        @ApiModelProperty(value = "错误代码")
        private String errorCode;

        @ApiModelProperty(value = "描述")
        private String description;

        @ApiModelProperty(value = "交易号")
        private String transactionNo;

        @ApiModelProperty(value = "SAT交易号")
        private String satTransactionNo;
    }

    /**
     * 金额信息
     */
    @Data
    @ApiModel(value = "金额信息")
    public static class Amount {
        @ApiModelProperty(value = "代理费")
        private String commision;

        @ApiModelProperty(value = "总金额")
        private String totalAmount;

        @ApiModelProperty(value = "代理费率")
        private String commisionRate;

        @ApiModelProperty(value = "净退款")
        private String netRefund;

        @ApiModelProperty(value = "其他扣除")
        private String otherDeduction;

        @ApiModelProperty(value = "税费列表")
        private List<Tax> taxs;

        @ApiModelProperty(value = "总税费")
        private String totalTaxs;

        @ApiModelProperty(value = "票号")
        private String ticketNo;
    }


    /**
     * 税费信息
     */
    @Data
    @ApiModel(value = "税费信息")
    public static class Tax {
        @ApiModelProperty(value = "税费名称")
        private String name;

        @ApiModelProperty(value = "税费金额")
        private String value;
    }
}
