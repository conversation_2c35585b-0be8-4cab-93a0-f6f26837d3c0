package com.swcares.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.core.util.StrUtils;
import com.swcares.entity.*;
import com.swcares.mapper.PnrCommandPartSsMapper;
import com.swcares.obj.dto.*;
import com.swcares.obj.vo.MnjxPlanSectionVo;
import com.swcares.service.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PnrCommandServicePartSs{
    @Resource
    private IMnjxFlightService iMnjxFlightService;
    @Resource
    private IMnjxPlanFlightService iMnjxPlanFlightService;
    @Resource
    private IMnjxAirportService iMnjxAirportService;
    @Resource
    private PnrCommandPartSsMapper pnrCommandPartSsMapper;
    @Resource
    private IMnjxOpenCabinService iMnjxOpenCabinService;
    @Resource
    private IMnjxCndService iMnjxCndService;
    @Resource
    private IMnjxPlaneModelService iMnjxPlaneModelService;
    @Resource
    private IPnrOperationService iPnrOperationService;

    public void ss(MemoryDataPnr memoryDataPnr, SsDto ssDto, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
    	log.debug(memoryDataPnr.getMemoryDataPnrId()+"SS业务层开始时间"+System.currentTimeMillis());
        if (CollUtil.isNotEmpty(memoryDataPnr.getPnrSegDtos())) {
            List<PnrSegDto> segs = memoryDataPnr.getPnrSegDtos().stream()
                    .filter(pnrSegDto -> !pnrSegDto.isXe())
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(segs) && segs.size() >= Constant.FOUR) {
                throw new UnifiedResultException(Constant.SEG_SIZE_ERROR);
            }
        }
        // 验证航班号是否存在
        List<MnjxFlight> mnjxFlights = iMnjxFlightService.lambdaQuery()
                .eq(MnjxFlight::getFlightNo, ssDto.getFlightNo())
                .list();
        if (CollUtil.isEmpty(mnjxFlights)) {
            throw new UnifiedResultException(Constant.FLIGHT_NUMBER);
        }
        //判断共享航班的是否被禁用
        if (StrUtil.isNotBlank(mnjxFlights.get(0).getCarrierFlight()) 
        		&& Constant.STR_ZERO.equals(mnjxFlights.get(0).getShareState())) {
        	throw new UnifiedResultException(Constant.UNABLE_TO_SELL);
		}
        // 验证航班日期比今天还早
        if (DateUtil.parse(ssDto.getDate(), DatePattern.NORM_DATE_PATTERN).before(DateUtil.parse(DateUtil.today(), DatePattern.NORM_DATE_PATTERN))) {
            throw new UnifiedResultException(Constant.DATE_ERROR);
        }

        List<MnjxPlanSectionVo> mnjxPlanSectionVos = pnrCommandPartSsMapper.retrieveSection(ssDto.getFlightNo(), ssDto.getDate());
        if (StrUtil.isNotBlank(mnjxFlights.get(0).getCarrierFlight()) ) {
        	mnjxPlanSectionVos = pnrCommandPartSsMapper.retrieveSection(mnjxFlights.get(0).getCarrierFlight(), ssDto.getDate());
		}
        StringBuilder errorStr = new StringBuilder();
        if (CollUtil.isEmpty(mnjxPlanSectionVos)) {
            String errMsg = this.constErrMsg(ssDto, Constant.NO_OP);
            throw new UnifiedResultException(errMsg);
        }
        // 航段校验
        MnjxAirport mnjxAirportOrg = iMnjxAirportService.lambdaQuery()
                .eq(MnjxAirport::getAirportCode, ssDto.getSegment().substring(0, 3))
                .one();
        MnjxAirport mnjxAirportDst = iMnjxAirportService.lambdaQuery()
                .eq(MnjxAirport::getAirportCode, ssDto.getSegment().substring(3))
                .one();
        if (ObjectUtil.isNull(mnjxAirportOrg) || ObjectUtil.isNull(mnjxAirportDst)) {
            String errMsg = this.constErrMsg(ssDto, Constant.SEGMENT);
            throw new UnifiedResultException(errMsg);
        }
        List<MnjxPlanSectionVo> mnjxPlanSectionVos1 = getSections(ssDto, mnjxPlanSectionVos, mnjxAirportOrg,
				mnjxAirportDst);
        // 验证航班是今天的航班，当前时间距离航班起飞时间不足一小时，甚至已经过了起飞时间
        DateTime nowDate = DateUtil.offset(DateUtil.parse(DateUtil.now(), DatePattern.NORM_DATETIME_MINUTE_PATTERN), DateField.HOUR, 1);
        if (DateUtil.parse(mnjxPlanSectionVos1.get(0).getFlightDate() + Constant.ONE_SPACE + mnjxPlanSectionVos1.get(0).getEstimateOff(), Constant.SS_DATE_CODE).before(nowDate)) {
            errorStr.append(Constant.CHECK_TKT_TIME);
            errorStr.append("\r\n");
            errorStr.append(Constant.FLT_BOOKING_LIMITED);
            throw new UnifiedResultException(errorStr.toString());
        }
        List<String> planSectionIds = mnjxPlanSectionVos1.stream().map(MnjxPlanSectionVo::getPlanSectionId).collect(Collectors.toList());
        // 查询相应日期的航班飞行计划，舱位不存在，或者舱位不可售
        List<MnjxOpenCabin> openCabins = iMnjxOpenCabinService.lambdaQuery()
        		.eq(MnjxOpenCabin::getSellCabin, ssDto.getCabin())
                .in(MnjxOpenCabin::getPlanSectionId, planSectionIds)
                .list();
        if (CollUtil.isEmpty(openCabins)) {
            String errMsg = this.constErrMsg(ssDto, Constant.CLASS);
            throw new UnifiedResultException(errMsg);
        }
        // 舱位状态正常，但剩余座位无
        boolean seatError = false;
        for (MnjxOpenCabin mnjxOpenCabin : openCabins) {
            if (Integer.parseInt(ssDto.getSeats()) > mnjxOpenCabin.getSeatAvailable()) {
                seatError = true;
                break;
            }
        }
        if (seatError) {
            throw new UnifiedResultException(Constant.UNABLE_TO_SELL);
        }
        checkData(memoryDataPnr, ssDto);
        log.debug(memoryDataPnr.getMemoryDataPnrId()+"SS业务层结束时间"+System.currentTimeMillis());
        log.debug(memoryDataPnr.getMemoryDataPnrId()+"SS构建缓存对象开始时间"+System.currentTimeMillis());
        this.buildData(ssDto, mnjxAirportOrg, mnjxAirportDst, mnjxPlanSectionVos1.get(0), memoryDataPnr, openCabins,mnjxOffice ,mnjxSi );
        log.debug(memoryDataPnr.getMemoryDataPnrId()+"SS构建缓存对象结束时间"+System.currentTimeMillis());
    }

	private List<MnjxPlanSectionVo> getSections(SsDto ssDto, List<MnjxPlanSectionVo> mnjxPlanSectionVos,
			MnjxAirport mnjxAirportOrg, MnjxAirport mnjxAirportDst) throws UnifiedResultException {
		List<MnjxPlanSectionVo> mnjxPlanSectionVos1 = new ArrayList<>();
        boolean findFirst = false;
        boolean notComplete = true;
        for (MnjxPlanSectionVo mnjxPlanSectionVo : mnjxPlanSectionVos) {
            if (mnjxPlanSectionVo.getDepAptId().equals(mnjxAirportOrg.getAirportId())
                    && mnjxPlanSectionVo.getArrAptId().equals(mnjxAirportDst.getAirportId())) {
                mnjxPlanSectionVos1.add(mnjxPlanSectionVo);
                notComplete = false;
                break;
            } else {
                if (mnjxPlanSectionVo.getDepAptId().equals(mnjxAirportOrg.getAirportId())) {
                	findFirst = true;
                    mnjxPlanSectionVos1.add(mnjxPlanSectionVo);
                } else if (mnjxPlanSectionVo.getArrAptId().equals(mnjxAirportDst.getAirportId())) {
                    mnjxPlanSectionVos1.add(mnjxPlanSectionVo);
                    notComplete = false;
                    break;
                } else {
                	if (findFirst) {
                		mnjxPlanSectionVos1.add(mnjxPlanSectionVo);
					}
                }
            }
        }
        if (CollUtil.isEmpty(mnjxPlanSectionVos1) || notComplete) {
            String errMsg = this.constErrMsg(ssDto, Constant.SEGMENT);
            throw new UnifiedResultException(errMsg);
        }
		return mnjxPlanSectionVos1;
	}

	private void checkData(MemoryDataPnr memoryDataPnr, SsDto ssDto) throws UnifiedResultException {
		// 行动代码校验
        if (!Constant.ACTION_CODE_NN.equalsIgnoreCase(ssDto.getActionCode())) {
            throw new UnifiedResultException(Constant.ACTION_CODE);
        }

        // 校验订座人数
        // 与GN中的团队人员数进行比较
        if (CollUtil.isNotEmpty(memoryDataPnr.getPnrGnDtos())) {
            List<PnrGnDto> pnrGnDtos = memoryDataPnr.getPnrGnDtos();
            PnrGnDto pnrGnDto = pnrGnDtos.stream()
                    .filter(g -> !g.isXe())
                    .collect(Collectors.toList())
                    .get(0);
            if (Integer.parseInt(ssDto.getSeats()) != pnrGnDto.getMnjxPnrGn().getGroupNumber()) {
                throw new UnifiedResultException(Constant.SEATS);
            }
        } else {
            // 判断nm人数
            List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
            if (CollUtil.isNotEmpty(pnrNmDtos)) {
                long count = pnrNmDtos.stream()
                        .filter(p -> StrUtil.isEmpty(p.getMnjxPnrNm().getChangeType()))
                        .count();
                if (count != Integer.parseInt(ssDto.getSeats())) {
                    throw new UnifiedResultException(Constant.SEATS);
                }
            }
        }
        // 判断ss人数
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
        if (CollUtil.isNotEmpty(pnrSegDtos)) {
            List<MnjxPnrSeg> mnjxPnrSegs = pnrSegDtos.stream().filter(pnrSegDto -> !pnrSegDto.isXe()).map(PnrSegDto::getMnjxPnrSeg).collect(Collectors.toList());
            for (MnjxPnrSeg mnjxPnrSeg : mnjxPnrSegs) {
                if ("SS".equalsIgnoreCase(mnjxPnrSeg.getPnrSegType()) || "SD".equalsIgnoreCase(mnjxPnrSeg.getPnrSegType())) {
                    if (mnjxPnrSeg.getSeatNumber() != Integer.parseInt(ssDto.getSeats())) {
                        throw new UnifiedResultException(Constant.SEATS);
                    }
                }
            }
        }
        // 行动代码校验
        if (!Constant.ACTION_CODE_NN.equalsIgnoreCase(ssDto.getActionCode())) {
            throw new UnifiedResultException(Constant.ACTION_CODE);
        }
	}

    /**
     * 拼接 错误 提示
     *
     * @param ssDto         参数对象
     * @param errorCategory 错误类别
     * @return 错我提示
     */
    private String constErrMsg(SsDto ssDto, String errorCategory) {
        String airlineCode = StrUtils.subPre(ssDto.getFlightNo(), 2);
        String no = StrUtils.subSuf(ssDto.getFlightNo(), 2);
        return StrUtils.format("{} {} {} {} {} {}{} {} \r\n{}",
                airlineCode, no, ssDto.getCabin(), DateUtils.ymd2Com(ssDto.getDate()), ssDto.getSegment(),
                ssDto.getActionCode(), ssDto.getSeats(), errorCategory, Constant.UNABLE_TO_SELL);
    }

    private void buildData(SsDto ssDto,
                           MnjxAirport orgPort,
                           MnjxAirport dstPort,
                           MnjxPlanSectionVo planSection,
                           MemoryDataPnr memoryDataPnr,
                           List<MnjxOpenCabin> openCabins,
                           MnjxOffice mnjxOffice,
                           MnjxSi mnjxSi) throws UnifiedResultException {
        if (ObjectUtil.isNull(memoryDataPnr.getMnjxPnr().getPnrId())) {
            iPnrOperationService.createNewPnr(memoryDataPnr,mnjxOffice,mnjxSi);
        }
        PnrSegDto pnrSegDto = new PnrSegDto();
        MnjxPnrSeg mnjxPnrSeg = new MnjxPnrSeg();
        pnrSegDto.setMnjxPnrSeg(mnjxPnrSeg);
        mnjxPnrSeg.setPnrSegId(IdUtil.getSnowflake(1, 1).nextIdStr());
        mnjxPnrSeg.setPnrId(memoryDataPnr.getMnjxPnr().getPnrId());
        mnjxPnrSeg.setPnrSegNo(memoryDataPnr.getPnrSegDtos().size() + 1);
        mnjxPnrSeg.setFlightNo(ssDto.getFlightNo());
        MnjxFlight flight = iMnjxFlightService.lambdaQuery().eq(MnjxFlight::getFlightNo, ssDto.getFlightNo()).one();
        if (StrUtil.isNotBlank(flight.getCarrierFlight())) {
        	pnrSegDto.setCarrierFlight(flight.getCarrierFlight());
        	mnjxPnrSeg.setCarrierFlight(flight.getCarrierFlight());
		}
        mnjxPnrSeg.setFlightDate(ssDto.getDate());
        mnjxPnrSeg.setCabinClass(openCabins.get(0).getCabinClass());
        mnjxPnrSeg.setSellCabin(ssDto.getCabin());
        mnjxPnrSeg.setOrg(orgPort.getAirportCode());
        mnjxPnrSeg.setDst(dstPort.getAirportCode());
        mnjxPnrSeg.setActionCode(StrUtils.format("{}{}", "DK", ssDto.getSeats()));
        mnjxPnrSeg.setEstimateOff(planSection.getEstimateOff());
        mnjxPnrSeg.setEstimateArr(planSection.getEstimateArr());
        mnjxPnrSeg.setPnrSegType("SS");
        mnjxPnrSeg.setSeatNumber(Integer.parseInt(ssDto.getSeats()));
        MnjxPlanFlight planFlight = iMnjxPlanFlightService.lambdaQuery()
                .eq(MnjxPlanFlight::getPlanFlightId, planSection.getPlanFlightId())
                .one();
        MnjxCnd cnd = iMnjxCndService.lambdaQuery().eq(MnjxCnd::getCndNo, planFlight.getCndNo()).one();
        MnjxPlaneModel planeModel = iMnjxPlaneModelService.lambdaQuery()
                .eq(MnjxPlaneModel::getPlaneModelId, cnd.getPlaneModelId())
                .one();
        mnjxPnrSeg.setPlaneVersion(planeModel.getPlaneModelType());
        mnjxPnrSeg.setRecreation(planeModel.getIsRecreation());
        mnjxPnrSeg.setMeal(planSection.getMealCode());
        String inputValue;
        if (StrUtil.isNotBlank(flight.getCarrierFlight())) {
        	inputValue = StrUtils.format(" {}{} {}   {}{}  {} DK{}   {} {}          {} {} 0 R E T1T2",
                    "*", ssDto.getFlightNo(), ssDto.getCabin(), DateUtils.ymd2WeekEn(ssDto.getDate()).substring(0, 2), DateUtils.ymd2Com(ssDto.getDate()), ssDto.getSegment(),
                    ssDto.getSeats(), planSection.getEstimateOff(), planSection.getEstimateArr(), planeModel.getPlaneModelType(), StrUtil.nullToDefault(planSection.getMealCode(), " "));
        }else {
        	inputValue = StrUtils.format("  {} {}   {}{}  {} DK{}   {} {}          {} {} 0 R E T1T2",
                    ssDto.getFlightNo(), ssDto.getCabin(), DateUtils.ymd2WeekEn(ssDto.getDate()).substring(0, 2), DateUtils.ymd2Com(ssDto.getDate()), ssDto.getSegment(),
                    ssDto.getSeats(), planSection.getEstimateOff(), planSection.getEstimateArr(), planeModel.getPlaneModelType(), StrUtil.nullToDefault(planSection.getMealCode(), " "));
		}
        mnjxPnrSeg.setInputValue(inputValue);
        memoryDataPnr.getPnrSegDtos().add(pnrSegDto);
        log.debug(memoryDataPnr.getMemoryDataPnrId()+"SS排序缓存对象开始时间"+System.currentTimeMillis());
        sortPnr(pnrSegDto, memoryDataPnr);
        log.debug(memoryDataPnr.getMemoryDataPnrId()+"SS排序缓存对象结束时间"+System.currentTimeMillis());
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos().stream()
                .sorted(Comparator.comparing(PnrSegDto::getPnrIndex))
                .collect(Collectors.toList());
        memoryDataPnr.getPnrSegDtos().clear();
        memoryDataPnr.getPnrSegDtos().addAll(pnrSegDtos);
        
        // 筛选出所有seg
        List<AbstractPnrDto> pnrSegs = new ArrayList<>();
        if (CollUtil.isNotEmpty(pnrSegDtos)) {
            pnrSegs = pnrSegDtos.stream().filter(it -> !it.isXe()).filter(it -> it instanceof PnrSegDto).collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(pnrSegs)) {
        	List<AbstractPnrDto> sortDtos = pnrSegs.stream().sorted(Comparator.comparing(it -> ((AbstractPnrDto)it).getPnrIndex())).collect(Collectors.toList());
            int pnrSegNo = 1;
            if (CollUtil.isNotEmpty(sortDtos)) {
    			for (AbstractPnrDto abstractPnrDto : sortDtos) {
    				((PnrSegDto) abstractPnrDto).getMnjxPnrSeg().setPnrSegNo(pnrSegNo);
    				pnrSegNo = pnrSegNo + 1;
    			}
    		}
		}
    }

    private void sortPnr(PnrSegDto pnrSegDto, MemoryDataPnr memoryDataPnr) {
        // 获取所有的pnr项
        List<AbstractPnrDto> abstractPnrDtos = memoryDataPnr.getAbstractPnrDtos().stream()
                .sorted(Comparator.comparing(AbstractPnrDto::getPnrIndex))
                .collect(Collectors.toList());
        // 筛选出所有seg
        List<AbstractPnrDto> pnrSegs = new ArrayList<>();
        if (CollUtil.isNotEmpty(abstractPnrDtos)) {
            pnrSegs = abstractPnrDtos.stream().filter(it -> !it.isXe()).filter(it -> it instanceof PnrSegDto).collect(Collectors.toList());
        }
        // 缓存里面添加了seg
        if (CollUtil.isNotEmpty(pnrSegs)) {
            int indexNum = 0;
            // 找出日期相同的seg
            List<AbstractPnrDto> sameDate = pnrSegs.stream().filter(it -> {
                PnrSegDto pnrSeg = (PnrSegDto) it;
                return StrUtil.isNotBlank(pnrSeg.getMnjxPnrSeg().getFlightDate())
                        && pnrSeg.getMnjxPnrSeg().getFlightDate().equals(pnrSegDto.getMnjxPnrSeg().getFlightDate());
            }).collect(Collectors.toList());
            // 如果不含有相同日期的seg
            if (CollUtil.isEmpty(sameDate)) {
                for (AbstractPnrDto abstractPnrDto : pnrSegs) {
                    PnrSegDto pnrSeg = (PnrSegDto) abstractPnrDto;
                    if (StrUtil.isNotBlank(pnrSeg.getMnjxPnrSeg().getFlightDate()) &&
                            DateUtil.parse(pnrSegDto.getMnjxPnrSeg().getFlightDate(), DatePattern.NORM_DATE_PATTERN).before(DateUtil.parse(pnrSeg.getMnjxPnrSeg().getFlightDate(), DatePattern.NORM_DATE_PATTERN))) {
                        indexNum = pnrSeg.getPnrIndex();
                        break;
                    }
                }
                if (indexNum == 0) {
                    indexNum = pnrSegs.get(pnrSegs.size() - 1).getPnrIndex() + 1;
                }
            } else {
                // 如果含有相同日期的seg
                //判断是否有第一个航段可接续
                for (AbstractPnrDto abstractPnrDto : sameDate) {
                    PnrSegDto pnrSeg = (PnrSegDto) abstractPnrDto;
                    if (pnrSeg.getMnjxPnrSeg().getDst().equals(pnrSegDto.getMnjxPnrSeg().getOrg())) {
                        indexNum = pnrSeg.getPnrIndex() + 1;
                        break;
                    }
                }
                //未找到航段可接续，找到第一个大于等于当前航班出发时刻的航段行
                if (indexNum == 0) {
                    for (AbstractPnrDto abstractPnrDto : sameDate) {
                        PnrSegDto pnrSeg = (PnrSegDto) abstractPnrDto;
                        if (StrUtil.isNotBlank(pnrSeg.getMnjxPnrSeg().getFlightDate()) &&
                                DateUtil.parse(pnrSegDto.getMnjxPnrSeg().getFlightDate() + " " + pnrSegDto.getMnjxPnrSeg().getEstimateOff(), "yyyy-MM-dd HHmm").before(DateUtil.parse(pnrSeg.getMnjxPnrSeg().getFlightDate()+ " " + pnrSeg.getMnjxPnrSeg().getEstimateOff(), "yyyy-MM-dd HHmm"))) {
                            indexNum = pnrSeg.getPnrIndex();
                            break;
                        }
                    }
                }
                //如果未找到符合条件的seg，则当前行插入同日期这一组的最后一行
                if (indexNum == 0) {
                    indexNum = pnrSegs.get(sameDate.size() - 1).getPnrIndex() + 1;
                }
            }
            for (AbstractPnrDto abstractPnrDto : abstractPnrDtos) {
                if (abstractPnrDto.getPnrIndex() >= indexNum) {
                    abstractPnrDto.setPnrIndex(abstractPnrDto.getPnrIndex() + 1);
                }
            }
            pnrSegDto.setPnrIndex(indexNum);
        }
    }

}
