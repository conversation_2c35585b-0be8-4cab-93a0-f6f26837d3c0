package com.swcares.eterm.dcs.fdc.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.*;
import com.swcares.entity.*;
import com.swcares.eterm.dcs.fdc.dto.CflParamDto;
import com.swcares.eterm.dcs.fdc.dto.CflPlanFlightDto;
import com.swcares.eterm.dcs.fdc.dto.CflSectionDto;
import com.swcares.eterm.dcs.fdc.mapper.CflMapper;
import com.swcares.eterm.dcs.fdc.service.ICflService;
import com.swcares.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * description：CFL:【航空公司二字码】/【日期】/【数据项】 数据项O、C、CL、S、I、N
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class CflServiceImpl implements ICflService {

    @Resource
    private CflMapper cflMapper;

    @Resource
    private IMnjxAirlineService iMnjxAirlineService;
    @Resource
    private IMnjxAirportService iMnjxAirportService;
    @Resource
    private IMnjxPlanSectionService iMnjxPlanSectionService;
    @Resource
    private IMnjxTcardSectionService iMnjxTcardSectionService;
    /**
     * CFL:CA/08MAY/PEK/O   显示CA航司5月8号值机城市是PEK的OPEN航班
     */
    private final static Pattern CFL_PATTERN = Pattern.compile("^CFL[:\\s](\\w{2})(/(\\w{5,7}|[+-.]))?(/(\\w{3}))?(/(\\w*))?$");

    @Override
    public CflParamDto parseCfl(String cmd) throws UnifiedResultException {
        if (!ReUtils.isMatch(CFL_PATTERN, cmd)) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        //参数解析
        List<String> allGroups = ReUtils.getAllGroups(CFL_PATTERN, cmd, false);
        String airlineCode = allGroups.get(0);
        String fltDate = allGroups.get(2);
        String city = allGroups.get(4);
        String option = allGroups.get(6);

        //参数验证
        //航司验证
        List<String> airlineList = Arrays.asList(Constant.AIRLINES);
        if (!airlineList.contains(airlineCode)) {
            throw new UnifiedResultException(Constant.AIRLINE);
        }
        //日期验证
        fltDate = StrUtil.isNotEmpty(fltDate) ? DateUtils.com2ymd(fltDate) : DateUtils.today();
        //航站验证
        MnjxOffice office = MemoryDataUtils.getMemoryData().getMnjxOffice();
        city = StrUtil.isEmpty(city) ? StrUtil.subPre(office.getOfficeNo(), 3) : city;
        MnjxAirport airport = iMnjxAirportService.lambdaQuery().eq(MnjxAirport::getAirportCode, city).one();
        if (ObjectUtil.isEmpty(airport)) {
            throw new UnifiedResultException(Constant.CITY);
        }
        //选项验证
        option = StrUtil.isNotEmpty(option) ? option : Constant.I;
        List<String> fltStatus = Arrays.asList(Constant.FLTSTATUS);
        if (!fltStatus.contains(option)) {
            throw new UnifiedResultException(Constant.OPTION);
        }
        //数据封装
        CflParamDto cflParamDto = new CflParamDto();
        cflParamDto.setAirlineCode(airlineCode);
        cflParamDto.setFltDate(fltDate);
        cflParamDto.setCity(city);
        cflParamDto.setAirportId(airport.getAirportId());
        cflParamDto.setOption(option);
        return cflParamDto;
    }

    @Override
    public List<CflPlanFlightDto> handle(CflParamDto cflParamDto) throws UnifiedResultException {
        MnjxAirline airline = iMnjxAirlineService.lambdaQuery()
                .eq(MnjxAirline::getAirlineCode, cflParamDto.getAirlineCode())
                .one();
        if (ObjectUtil.isEmpty(airline)) {
            throw new UnifiedResultException(Constant.AIRLINE);
        }
        //查询计划航班等数据
        List<CflPlanFlightDto> cflsDtoList = cflMapper.retrievePlanFlight(airline.getAirlineId(), cflParamDto.getFltDate());
        List<CflPlanFlightDto> cflDtoList = new ArrayList<>();
        if (CollUtils.isNotEmpty(cflsDtoList)) {
            List<String> planFlightIds = cflsDtoList.stream().map(CflPlanFlightDto::getPlanFlightId).collect(Collectors.toList());
            //查询计划航节
            List<MnjxPlanSection> planSections = iMnjxPlanSectionService.lambdaQuery()
                    .in(MnjxPlanSection::getPlanFlightId, planFlightIds)
                    .eq(MnjxPlanSection::getDepAptId, cflParamDto.getAirportId())
                    .list();
            List<String> planFlights = planSections.stream().map(MnjxPlanSection::getPlanFlightId).collect(Collectors.toList());
            //筛选过滤出包含目标航站的航班数据
            cflsDtoList = cflsDtoList.stream().filter(c -> planFlights.contains(c.getPlanFlightId())).collect(Collectors.toList());
            String option = cflParamDto.getOption();
            if (Constant.S.equals(option)) {
                cflDtoList = handleCflS(cflsDtoList, planSections);
            } else {
                cflDtoList = handleCflOption(cflParamDto, cflsDtoList);
            }
        }
        return cflDtoList;
    }

    /**
     * 如果参数为S，则不再按航班状态进行过滤，直接返回上一步查询出的所有航班
     */
    private List<CflPlanFlightDto> handleCflS(List<CflPlanFlightDto> cflsDtoList, List<MnjxPlanSection> planSections) {
        List<String> tcardIds = cflsDtoList.stream().map(CflPlanFlightDto::getTcardId).collect(Collectors.toList());
        //查询TcardSection
        List<MnjxTcardSection> tcardSections = iMnjxTcardSectionService.lambdaQuery().in(MnjxTcardSection::getTcardId, tcardIds).list();
        //数据组装 构建航节数据
        this.dataAssemblingSection(cflsDtoList, planSections, tcardSections);
        return cflsDtoList;
    }

    /**
     * 构建航节数据
     */
    private void dataAssemblingSection(List<CflPlanFlightDto> cflsDtoList, List<MnjxPlanSection> planSections, List<MnjxTcardSection> tcardSections) {
        for (CflPlanFlightDto cflPlanFlightDto : cflsDtoList) {
            List<CflSectionDto> sectionDtos = new ArrayList<>();
            List<MnjxPlanSection> sectionList = planSections.stream().filter(s -> cflPlanFlightDto.getPlanFlightId().equals(s.getPlanFlightId())).collect(Collectors.toList());
            if (CollUtils.isEmpty(sectionList)) {
                continue;
            }
            //单航段处理
            if (sectionList.size() == Constant.ONE) {
                MnjxPlanSection planSection = sectionList.get(0);
                //起始站
                CflSectionDto orgSection = new CflSectionDto();
                String org = getAirportCode(planSection.getDepAptId());
                orgSection.setCity(org);
                String offTime = StrUtil.isNotEmpty(planSection.getActualOff()) ? StrUtil.format("{}{}", planSection.getActualOff(), StrUtil.isNotEmpty(planSection.getActualOffChange()) ? planSection.getActualOffChange() : StrUtils.EMPTY) : StrUtil.format("{}{}", planSection.getEstimateOff(), StrUtils.isNotEmpty(planSection.getEstimateOffChange()) ? planSection.getEstimateOffChange() : StrUtil.EMPTY);
                orgSection.setOffTime(offTime);
                String aboardTime = StrUtil.isNotEmpty(planSection.getActualBoarding()) ? StrUtil.format("{}{}", planSection.getActualBoarding(), StrUtil.isNotEmpty(planSection.getActualBoardingChange()) ? planSection.getActualBoardingChange() : StrUtils.EMPTY) : StrUtil.format("{}{}", planSection.getEstimateBoarding(), StrUtils.isNotEmpty(planSection.getEstimateBoardingChange()) ? planSection.getEstimateBoardingChange() : StrUtil.EMPTY);
                orgSection.setAboardTime(aboardTime);
                orgSection.setStat(cflPlanFlightDto.getCkStatus());
                orgSection.setGates(StrUtil.isNotEmpty(planSection.getGate()) ? planSection.getGate() : "????");
                orgSection.setSsel(cflPlanFlightDto.getSsel());
                orgSection.setEquip(StrUtil.format("{}/{}", cflPlanFlightDto.getEqt(), cflPlanFlightDto.getVers()));
                orgSection.setPos("GATE");
                orgSection.setType("NAM FDC");
                sectionDtos.add(orgSection);
                //到达站
                CflSectionDto dsgSection = new CflSectionDto();
                String dst = getAirportCode(planSection.getArrAptId());
                dsgSection.setCity(dst);
                String arrTime = StrUtil.isNotEmpty(planSection.getActualArr()) ? StrUtil.format("{}{}", planSection.getActualArr(), StrUtil.isNotEmpty(planSection.getActualArrChange()) ? planSection.getActualArrChange() : StrUtil.EMPTY) : StrUtil.format("{}{}", planSection.getEstimateArr(), StrUtil.isNotEmpty(planSection.getEstimateArrChange()) ? planSection.getEstimateArrChange() : StrUtil.EMPTY);
                dsgSection.setArrTime(arrTime);
                sectionDtos.add(dsgSection);
            }
            //多航段处理
            else {
                List<MnjxTcardSection> tcardSectionList = tcardSections.stream().filter(t -> cflPlanFlightDto.getTcardId().equals(t.getTcardId())).collect(Collectors.toList());
                for (int i = 0; i < tcardSectionList.size(); i++) {
                    MnjxTcardSection tcardSection = tcardSections.get(i);
                    for (int j = 0; j < sectionList.size(); j++) {
                        MnjxPlanSection planSection = sectionList.get(j);
                        // 第一个航站
                        if (j == 0 && tcardSection.getAirportId().equals(planSection.getDepAptId())) {
                            //设置起始站
                            CflSectionDto orgSection = new CflSectionDto();
                            String org = getAirportCode(tcardSection.getAirportId());
                            orgSection.setCity(org);
                            String offTime = StrUtil.isNotEmpty(planSection.getActualOff()) ? StrUtil.format("{}{}", planSection.getActualOff(), StrUtil.isNotEmpty(planSection.getActualOffChange()) ? planSection.getActualOffChange() : StrUtils.EMPTY) : StrUtil.format("{}{}", planSection.getEstimateOff(), StrUtils.isNotEmpty(planSection.getEstimateOffChange()) ? planSection.getEstimateOffChange() : StrUtil.EMPTY);
                            orgSection.setOffTime(offTime);
                            String aboardTime = StrUtil.isNotEmpty(planSection.getActualBoarding()) ? StrUtil.format("{}{}", planSection.getActualBoarding(), StrUtil.isNotEmpty(planSection.getActualBoardingChange()) ? planSection.getActualBoardingChange() : StrUtils.EMPTY) : StrUtil.format("{}{}", planSection.getEstimateBoarding(), StrUtils.isNotEmpty(planSection.getEstimateBoardingChange()) ? planSection.getEstimateBoardingChange() : StrUtil.EMPTY);
                            orgSection.setAboardTime(aboardTime);
                            orgSection.setStat(cflPlanFlightDto.getCkStatus());
                            orgSection.setGates(StrUtil.isNotEmpty(planSection.getGate()) ? planSection.getGate() : "????");
                            orgSection.setSsel(cflPlanFlightDto.getSsel());
                            orgSection.setPos("GATE");
                            orgSection.setType("NAM FDC");
                            orgSection.setEquip(StrUtil.format("{}/{}", cflPlanFlightDto.getEqt(), cflPlanFlightDto.getVers()));
                            sectionDtos.add(orgSection);
                            break;
                        }
                        // 中间的航站
                        else if (tcardSection.getAirportId().equals(planSection.getDepAptId()) && tcardSections.get(j + 1).getAirportId().equals(planSection.getArrAptId())) {
                            CflSectionDto orgSection = new CflSectionDto();
                            String org = getAirportCode(tcardSection.getAirportId());
                            orgSection.setCity(org);
                            String offTime = StrUtil.isNotEmpty(planSection.getActualOff()) ? StrUtil.format("{}{}", planSection.getActualOff(), StrUtil.isNotEmpty(planSection.getActualOffChange()) ? planSection.getActualOffChange() : StrUtils.EMPTY) : StrUtil.format("{}{}", planSection.getEstimateOff(), StrUtils.isNotEmpty(planSection.getEstimateOffChange()) ? planSection.getEstimateOffChange() : StrUtil.EMPTY);
                            orgSection.setOffTime(offTime);
                            String aboardTime = StrUtil.isNotEmpty(planSection.getActualBoarding()) ? StrUtil.format("{}{}", planSection.getActualBoarding(), StrUtil.isNotEmpty(planSection.getActualBoardingChange()) ? planSection.getActualBoardingChange() : StrUtils.EMPTY) : StrUtil.format("{}{}", planSection.getEstimateBoarding(), StrUtils.isNotEmpty(planSection.getEstimateBoardingChange()) ? planSection.getEstimateBoardingChange() : StrUtil.EMPTY);
                            orgSection.setAboardTime(aboardTime);
                            orgSection.setStat(cflPlanFlightDto.getCkStatus());
                            orgSection.setGates(StrUtil.isNotEmpty(planSection.getGate()) ? planSection.getGate() : "????");
                            orgSection.setSsel(cflPlanFlightDto.getSsel());
                            orgSection.setPos("GATE");
                            orgSection.setType("NAM FDC");
                            orgSection.setEquip(StrUtil.format("{}/{}", cflPlanFlightDto.getEqt(), cflPlanFlightDto.getVers()));
                            log.info("航班号：" + cflPlanFlightDto.getFlightNo() + "id" + cflPlanFlightDto.getPlanFlightId());
                            String arrTime = StrUtil.isNotEmpty(sectionList.get(i - 1).getActualArr()) ? StrUtil.format("{}{}", sectionList.get(i - 1).getActualArr(), sectionList.get(i - 1).getActualArrChange()) : StrUtil.format("{}{}", sectionList.get(i - 1).getEstimateArr(), sectionList.get(i - 1).getEstimateArrChange());
                            orgSection.setArrTime(arrTime);
                            sectionDtos.add(orgSection);
                            break;
                        }
                        // 最后一个航站
                        else if (j == sectionList.size() - 1 && tcardSection.getAirportId().equals(planSection.getArrAptId())) {
                            CflSectionDto dsgSection = new CflSectionDto();
                            String dst = getAirportCode(tcardSection.getAirportId());
                            dsgSection.setCity(dst);
                            String arrTime = StrUtil.isNotEmpty(planSection.getActualArr()) ? StrUtil.format("{}{}", planSection.getActualArr(), planSection.getActualArrChange()) : StrUtil.format("{}{}", planSection.getEstimateArr(), planSection.getEstimateArrChange());
                            dsgSection.setArrTime(arrTime);
                            sectionDtos.add(dsgSection);
                            break;
                        }
                    }
                }
            }
            cflPlanFlightDto.setSectionDtos(sectionDtos);
        }
    }

    /**
     * 获取航站三字码
     */
    private String getAirportCode(String aptId) {
        MnjxAirport airport = iMnjxAirportService.getById(aptId);
        if (ObjectUtil.isNotEmpty(airport)) {
            return airport.getAirportCode();
        }
        return StrUtil.EMPTY;
    }

    /**
     * 根据条件过滤查询航班
     */
    private List<CflPlanFlightDto> handleCflOption(CflParamDto cflParamDto, List<CflPlanFlightDto> cflsDtoList) {
        switch (cflParamDto.getOption()) {
            case Constant.O:
                //OPEN 航班，初始化状态为已初始化且SY 中显示航班状态为OP状态的航班
                cflsDtoList = cflsDtoList.stream().filter(c -> Constant.OPEN.equals(c.getCkStatus())).collect(Collectors.toList());
                break;
            case Constant.I:
                //初始化状态为已初始化的航班
                cflsDtoList = cflsDtoList.stream().filter(c -> Constant.Y.equals(c.getIsFlightInitial())).collect(Collectors.toList());
                break;
            case Constant.C:
                //CC 关闭航班，初始化状态为已初始化且SY 中显示航班状态为CC的航班
                cflsDtoList = cflsDtoList.stream().filter(c -> Constant.CC.equals(c.getCkStatus())).collect(Collectors.toList());
                break;
            case Constant.N:
                // 初始化状态为未初始化的航班
                cflsDtoList = cflsDtoList.stream().filter(c -> !Constant.Y.equals(c.getIsFlightInitial())).collect(Collectors.toList());
                break;
            case Constant.CL:
                //中间关闭的航班，初始化状态为已初始化且SY 中显示航班状态为“CCL”的航班
                cflsDtoList = cflsDtoList.stream().filter(c -> Constant.CL.equals(c.getCkStatus())).collect(Collectors.toList());
                break;
            default:
                break;
        }
        return cflsDtoList;
    }
}
