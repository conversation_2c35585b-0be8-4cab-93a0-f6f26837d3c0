package com.swcares.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.function.PageCheck;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxAirport;
import com.swcares.entity.MnjxGate;
import com.swcares.service.IGateService;
import com.swcares.obj.vo.GateQueryVo;
import com.swcares.obj.vo.GateVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> by yaodan
 * 2021/8/23-13:15
 */
@Api(tags = "登机口管理")
@SwaggerGroup(SwaggerGroup.GroupType.COMMON)
@RestController
@RequestMapping("/gate")
@Slf4j
public class GateController {

    @Resource
    private IGateService iGateService;


    @ApiOperation("新增登机口信息")
    @PostMapping("/create")
    public String create(@RequestBody @Valid MnjxGate mnjxGate) throws UnifiedResultException {
        boolean isOk = iGateService.create(mnjxGate);
        if (isOk) {
            return Constant.CREATE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.CREATE_FAIL);
        }
    }


    @ApiOperation("分页查询登机口信息")
    @PostMapping("/retrieveListByPage/{current}/{limit}")
    @PageCheck
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "当前页码",
                    name = "current",
                    required = true,
                    dataTypeClass = long.class,
                    paramType = Constant.PARAM_TYPE_PATH
            ),
            @ApiImplicitParam(
                    value = "每页记录数",
                    name = "limit",
                    required = true,
                    dataTypeClass = long.class,
                    paramType = Constant.PARAM_TYPE_PATH
            ),
            @ApiImplicitParam(
                    value = "查询对象",
                    name = "gateQueryVo",
                    dataTypeClass = GateQueryVo.class,
                    paramType = Constant.PARAM_TYPE_BODY
            )
    })
    public IPage<GateVo> retrievePageByCond(@PathVariable long current, @PathVariable long limit, @RequestBody GateQueryVo gateQueryVo) {
        return iGateService.retrievePageByCond(new Page<>(current, limit), gateQueryVo);
    }

    @ApiOperation("根据id获取登机口信息")
    @GetMapping("/retrieveById/{id}")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "登机口id",
                    name = "id",
                    required = true,
                    dataTypeClass = String.class,
                    paramType = Constant.PARAM_TYPE_PATH
            )
    })
    public MnjxGate retrieveById(@PathVariable String id) {
        return iGateService.retrieveById(id);
    }

    @ApiOperation("通过条件查询登机口")
    @PostMapping("/retrieveByCond")
    public List<MnjxGate> retrieveByCond(@RequestBody GateQueryVo gateQueryVo) {
        return iGateService.retrieveByCond(gateQueryVo);
    }

    @ApiOperation("下拉查询所有机场")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "机场三字码",
                    name = "airportCode",
                    dataTypeClass = String.class,
                    paramType = Constant.PARAM_TYPE_QUERY
            )
    })
    @PostMapping("/retrieveByAirportCode")
    public List<MnjxAirport> retrieveByAirportCode(String airportCode) {
        return iGateService.retrieveByAirportCode(airportCode);
    }

    @ApiOperation("修改登机口信息")
    @PostMapping("/updateById")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "mnjxGate",
                    value = "国家信息对象",
                    required = true,
                    dataTypeClass = MnjxGate.class,
                    paramType = Constant.PARAM_TYPE_BODY
            )
    })
    public String updateById(@RequestBody @Valid MnjxGate mnjxGate) throws UnifiedResultException {
        boolean isOk = iGateService.updateById(mnjxGate);
        if (isOk) {
            return Constant.UPDATE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.UPDATE_FAIL);
        }
    }


    @ApiOperation(value = "根据id批量删除", notes = "根据id批量删除")
    @DeleteMapping("/deleteByIds")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "登机口ID列表",
                    name = "ids",
                    required = true,
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = List.class,
                    example = "1493875375885201410,1494133925735784450,1496667904624992258"
            )
    })
    public String deleteByIds(@RequestBody List<String> ids) throws UnifiedResultException {
        boolean isOk = iGateService.deleteByIds(ids);
        if (isOk) {
            return Constant.DELETE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.DELETE_FAIL);
        }
    }
}
