package com.swcares.service.bkc;

import com.swcares.core.exception.SguiResultException;
import com.swcares.obj.dto.SplitPnrByPassengerDto;
import com.swcares.obj.vo.SplitPnrByPassengerVo;

/**
 * 分离PNR服务接口
 *
 * <AUTHOR>
 * @date 2025/1/15 16:30
 */
public interface ISplitPnrService {

    /**
     * 分离旅客
     *
     * @param dto 分离旅客请求参数
     * @return 分离结果
     * @throws SguiResultException 异常
     */
    SplitPnrByPassengerVo splitPnrByPassenger(SplitPnrByPassengerDto dto) throws SguiResultException;
}
