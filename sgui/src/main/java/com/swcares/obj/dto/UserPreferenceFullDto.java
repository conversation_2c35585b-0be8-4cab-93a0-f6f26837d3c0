package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 用户偏好设置完整数据传输对象
 *
 * <AUTHOR>
 * @since 2025/4/24 11:40
 */
@Data
@ApiModel(value = "UserPreferenceFullDto", description = "用户偏好设置完整数据传输对象")
public class UserPreferenceFullDto {

    @ApiModelProperty(value = "CT联系方式")
    private String ct;

    @ApiModelProperty(value = "CTCT联系方式")
    private String ctct;

    @ApiModelProperty(value = "CTCE联系方式")
    private String ctce;

    @ApiModelProperty(value = "是否非共享")
    private Boolean unshared;

    @ApiModelProperty(value = "是否仅直飞")
    private Boolean nonstop;

    @ApiModelProperty(value = "是否自动选择舱位")
    private Boolean autoSelectCabinClass;

    @ApiModelProperty(value = "是否自动占位")
    private Boolean autoOccupy;

    @ApiModelProperty(value = "是否使用QTB")
    private Boolean useQtb;

    @ApiModelProperty(value = "国内打票机号")
    private String domesticPrinterno;

    @ApiModelProperty(value = "国际打票机号")
    private String internationalPrinterno;

    @ApiModelProperty(value = "是否手动查询")
    private Boolean manualQuery;

    @ApiModelProperty(value = "是否回填英文姓名")
    private Boolean backfieldEnName;

    @ApiModelProperty(value = "航司CTCT设置列表")
    private List<AirlineCtctDto> airlinesCTCT;

    @ApiModelProperty(value = "检查提示设置")
    private CheckTipDto checkTrip;

    @ApiModelProperty(value = "旅客信息设置")
    private PassengerInfoDto passengerInfo;

    @ApiModelProperty(value = "备注列表")
    private List<String> remarkList;

    /**
     * 航司CTCT设置数据传输对象
     */
    @Data
    @ApiModel(value = "AirlineCtctDto", description = "航司CTCT设置数据传输对象")
    public static class AirlineCtctDto {
        @ApiModelProperty(value = "航司代码")
        private String airline;

        @ApiModelProperty(value = "CTCT值")
        private String ctct;
    }

    /**
     * 检查提示设置数据传输对象
     */
    @Data
    @ApiModel(value = "CheckTipDto", description = "检查提示设置数据传输对象")
    public static class CheckTipDto {
        @ApiModelProperty(value = "是否检查城市名称")
        private Boolean cityName;

        @ApiModelProperty(value = "是否检查机场名称")
        private Boolean airportName;

        @ApiModelProperty(value = "是否检查旅客信息")
        private Boolean passengerInformation;

        @ApiModelProperty(value = "是否检查含税价格")
        private Boolean priceIncludingTax;

        @ApiModelProperty(value = "是否检查航司名称")
        private Boolean airlineNameCheckSwitch;
    }

    /**
     * 旅客信息设置数据传输对象
     */
    @Data
    @ApiModel(value = "PassengerInfoDto", description = "旅客信息设置数据传输对象")
    public static class PassengerInfoDto {
        @ApiModelProperty(value = "旅客国籍")
        private String passengerNationality;

        @ApiModelProperty(value = "签证签发国")
        private String visaIssueCountry;
    }
}
