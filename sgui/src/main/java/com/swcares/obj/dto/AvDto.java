package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/4/10 14:14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "AvDto", description = "AV航班查询条件")
public class AvDto {

    /**
     * 出发机场
     */
    @ApiModelProperty(value = "出发机场")
    @NotNull(message = "出发机场不能为空")
    private String org;

    /**
     * 到达机场
     */
    @ApiModelProperty(value = "到达机场")
    @NotNull(message = "到达机场不能为空")
    private String dst;

    /**
     * 出发日期
     */
    @ApiModelProperty(value = "出发日期")
    @NotNull(message = "出发日期不能为空")
    private String flightDate;

    /**
     * 航司
     */
    @ApiModelProperty(value = "航司")
    private String airline;

    /**
     * 航班号
     */
    @ApiModelProperty(value = "航班号")
    private String flightNo;

    /**
     * 出发时间
     */
    @ApiModelProperty(value = "出发时间")
    private String estimateOffTime;

    /**
     * 中转点
     */
    @ApiModelProperty(value = "中转点")
    private String stopPoint;

    /**
     * 是否共享
     */
    @ApiModelProperty(value = "是否共享")
    private boolean isShare;

    /**
     * 仅直飞
     */
    @ApiModelProperty(value = "仅直飞")
    private boolean isNoStop;

    /**
     * 最低公布价
     */
    @ApiModelProperty(value = "最低公布价")
    private boolean lowestPrice;
}
