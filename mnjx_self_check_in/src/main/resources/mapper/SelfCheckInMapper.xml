<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.SelfCheckInMapper">

    <select id="retrievePassengerTripByIdCard" resultType="com.swcares.obj.vo.PassengerTripVo">
        SELECT
            nm.pnr_nm_id,
            seg.pnr_seg_id,
            cki.psg_cki_id,
            seat.psg_seat_id,
            nm.`name`,
            seg.org orgAirportCode,
            seg.dst dstAirportCode,
            seg.flight_no,
            seg.flight_date,
            seg.cabin_class,
            seg.estimate_off,
            seg.estimate_arr,
            cki.cki_status,
            seat.psg_seat seatNo,
            cki.aboard_no bnNo,
            cki.gate
        FROM
            mnjx_nm_ssr ssr
            left join mnjx_pnr_nm nm on ssr.pnr_nm_id = nm.pnr_nm_id
            left join mnjx_pnr mp on mp.pnr_id = nm.pnr_id
            left join mnjx_pnr_seg seg on nm.pnr_id = seg.pnr_id
            left join mnjx_psg_cki cki on nm.pnr_nm_id = cki.pnr_nm_id
            left join mnjx_psg_seat seat on cki.psg_cki_id = seat.psg_cki_id
        WHERE
            ssr.input_value LIKE concat ('%',#{idCard},'%')
            and seg.flight_date = #{today}
            and mp.pnr_status != 'DEL'
        ORDER BY flight_date, estimate_off
    </select>

    <select id="retrievePlanFlight" resultType="com.swcares.entity.MnjxPlanFlight">
        SELECT
            mpf.*
        FROM
            mnjx_plan_flight mpf
            left join mnjx_tcard mt on mpf.tcard_id = mt.tcard_id
            left join mnjx_flight mf on mt.flight_id = mf.flight_id
        where
            mpf.flight_date = #{flightDate}
            and mf.flight_no = #{flightNo}
    </select>

    <select id="retrieveTcardSection" resultType="com.swcares.entity.MnjxTcardSection">
        SELECT
            mts.*
        FROM
            mnjx_flight mf
            LEFT JOIN mnjx_tcard mt ON mf.flight_id = mt.flight_id
            LEFT JOIN mnjx_tcard_section mts ON mts.tcard_id = mt.tcard_id
        WHERE
            mf.flight_no = #{flightNo}
        order by mts.section_no
    </select>
</mapper>