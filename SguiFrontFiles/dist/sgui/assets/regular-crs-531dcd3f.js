const s=/^[a-zA-Z\d]{14,20}$/,a=/^[a-zA-Z\d]{3}$/,E=/^[0-9a-zA-Z]{2}$/,A=/^[a-zA-Z]{2,3}$/,_=/^(IC|WV).*/,t=/^(IC).*/,N=/^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2}))|([0]))$/,c=/^(\d+(.(\d)+)?)?$/,T=/^[a-zA-Z0-9\d]{1,22}$/,n=/^[0-9A-Za-z]{1,20}$/,o=/^(IC|WV)$/,R=/^(([1-9][0-9]*)|([0]\.([1-9]|[0-9][1-9]))|([1-9][0-9]*\.\d{1,2}))$/,I=/^(([1-9][0-9]*)|(0\.([0-9]|[0-9][0-9]))|(0)|([1-9][0-9]*\.\d{1,2}))$/,$=/^[0-9a-zA-Z]{2}$/,C=/^[0-9]{12}$/,d=/^\d{1,4}$/,S=/^[a-zA-Z]{4}$/,O=/^[0-9]{10}$/,M=/^\d{5}$|^\d{10}$/,z=/^\d{10}$/,Z=/^\d{5}$/,D=/^[1-9]{1}\d*$/,U=/^[0-9]*$/,L=/^[1-9][0-9]{0,2}$/,e=/^[a-zA-Z\d]{15,18}$/,f=/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/,u=/^(([1-9]\d*)|0)(\.\d+)?$/,F=/^[0-9]+$/,P=/^[1-9][0-9]{0,4}|-1$/,p=/^\d{4}$/,G=/^[0-9]{6}$/,m=/^[A-Za-z]{3}$/,r=/^([a-zA-Z]{1}[0-9]{1})|^([0-9]{1}[a-zA-Z]{1})|^([A-Za-z]{2})$/,i=/[/ ][pP][1-9][0-9]*(\/[pP]?[1-9][0-9]*)*$/,B=/#\d{4,8}/,l=/\d{1,2}[A-z]{3}\d/,K=/\d{8}(?= P|\/P)/,H=/[A-Z]{3}/,X=/^[\p{Script=Han}a-zA-Z0-9()·]*$/u,Y=/\p{Script=Han}/u,b=/(^\s*[a-zA-Z·]+[a-zA-Z\s·]*\/\s*[a-zA-Z·]+[a-zA-Z.\s·]*[a-zA-Z0-9()·]*$)/,V=/(^\s*[a-zA-Z]+[a-zA-Z\s]*\/?\s*[a-zA-Z]+[a-zA-Z.\s]*$)/,W=/^[A-Za-z\d]{3,7}$/,g=/^[A-Za-z]{1}$/,x=/^[A-Za-z\d]{2}$|^\*[A-Za-z\d]{2}$/,J=/^\d{2}[A-Za-z]{3}$/,j=/^\d{2}[A-Za-z]{3}\d{2}$/,k=/^\d{1,2}[A-Za-z]{3}(\d{2})?$/,y=/^[A-Za-z]{6}$/,h=/[\u4e00-\u9fa5]+/,q=/^(NN|LL)?\d{1,4}$/,v=/^([\p{Script=Han}\u2E80-\uFE4F]+(\s*[a-zA-Z]*))|([\p{Script=Han}\u2E80-\uFE4F]+([a-zA-Z]*))$/u,w=/(^\s*[0-9a-zA-Z]+[0-9a-zA-Z\s]*\/?\s*[0-9a-zA-Z]*[0-9a-zA-Z/()\s]*$)/,Q=/(^\s*[0-9a-zA-Z]+[0-9a-zA-Z\s]*\/?\s*[0-9a-zA-Z]*[0-9a-zA-Z\s]*$)/,ss=/^[0-9A-Za-z(~!@#$%^&*]{0,20}$/,as=/^[0-9A-Za-z]{0,2}$/,Es=/^\d{3}$/,As=/^\d{10}(-\d{2})?$/,_s=/^\d{1}$/,ts=/^[A-Za-z]+$/,Ns=/^\d{4}$/,cs=/^\d{2}[A-Za-z]{3}\d{2}/,Ts=/([01]\d|2[0-3])[0-5]\d/,ns=/^\d{6}$/,os=/^([01]\d|2[0-3])[0-5]\d$/,Rs=/^(?!.*(?:script|embed|style|img|image|object|frame|iframe|frameset|meta|xml|link|applet|onload|alert|SCRIPT|EMBED|STYLE|IMG|IMAGE|OBJECT|FRAME|IFRAME|FRAMESET|META|XML|LINK|APPLET|ONLOAD|ALERT)).*$/,Is=/^(?!.*(?:script|embed|style|img|image|object|frame|iframe|frameset|meta|xml|link|applet|onload|alert|SCRIPT|EMBED|STYLE|IMG|IMAGE|OBJECT|FRAME|IFRAME|FRAMESET|META|XML|LINK|APPLET|ONLOAD|ALERT)).*$/,$s=/^(?:\d{3}[-\s]?\d{9}|\d{9})$/,Cs=/^[a-zA-Z]+$/,ds=/[\u3002\uff1b\uff0c\uff1a\u201c\u201d\uff08\uff09\u3001\uff1f\u300a\u300b\uff01\u3010\u3011\uffe5]+/;export{g as $,E as A,ds as B,A as C,F as D,Z as E,S as F,r as G,L as H,Y as I,H as J,l as K,K as L,i as M,b as N,B as O,n as P,e as Q,$s as R,Ns as S,X as T,f as U,P as V,G as W,m as X,u as Y,p as Z,W as _,a,J as a0,j as a1,y as a2,q as a3,Ts as a4,k as a5,os as a6,x as a7,as as a8,cs as a9,O as aa,M as ab,D as ac,U as ad,Rs as ae,Is as af,N as b,I as c,$ as d,Q as e,o as f,_ as g,s as h,T as i,t as j,v as k,d as l,R as m,c as n,C as o,Es as p,As as q,_s as r,ts as s,w as t,z as u,V as v,Cs as w,h as x,ss as y,ns as z};
