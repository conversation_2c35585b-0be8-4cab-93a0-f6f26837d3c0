package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/4/23 14:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CheckVersionDto", description = "checkVersion接口参数")
public class CheckVersionDto {

    /**
     * app
     */
    @ApiModelProperty(value = "app")
    private String app;

    /**
     * module
     */
    @ApiModelProperty(value = "module")
    private String module;

    /**
     * version
     */
    @ApiModelProperty(value = "version")
    private String version;

    /**
     * timestamp
     */
    @ApiModelProperty(value = "timestamp")
    private String timestamp;

    /**
     * versionAttr
     */
    @ApiModelProperty(value = "versionAttr")
    private String versionAttr;

    @ApiModelProperty(value = "userInfo")
    private UserInfo userInfo;

    /**
     * UserInfo
     */
    @Data
    public static class UserInfo {
        @ApiModelProperty(value = "office")
        private String office;

        @ApiModelProperty(value = "airline")
        private String airline;

        @ApiModelProperty(value = "airport")
        private String airport;
    }
}
