/*
 Navicat Premium Data Transfer

 Source Server         : aliyun
 Source Server Type    : MySQL
 Source Server Version : 50732 (5.7.32-log)
 Source Host           : rm-f8zld8y563d53n50ojo.mysql.rds.aliyuncs.com:3306
 Source Schema         : mnjx

 Target Server Type    : MySQL
 Target Server Version : 50732 (5.7.32-log)
 File Encoding         : 65001

 Date: 23/03/2023 17:03:10
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for mnjx_office
-- ----------------------------
DROP TABLE IF EXISTS `mnjx_office`;
CREATE TABLE `mnjx_office`  (
  `office_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'ID',
  `office_no` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'OFFICE号，OFFICE号将被当成ETERM账号用于ETERM登录',
  `office_type` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'OFFICE所属机构分类 0,代理人。1,机场。2,航空公司。',
  `org_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'OFFICE号所属机构ID，根据OFFICE_TYPE分别关联到agent表，airline表，airport表',
  `office_status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '状态 1,启用 0,停用',
  `office_password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'OFFICE号密码，OFFICE号将被当成ETERM账号用于登录！！！',
  PRIMARY KEY (`office_id`) USING BTREE,
  UNIQUE INDEX `office_no_unique`(`office_no`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'OFFICE信息表，各代理人、机场、航司所拥有的OFFICE均在此分配' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mnjx_office
-- ----------------------------
INSERT INTO `mnjx_office` VALUES ('1568236956800782337', 'SHA001', '0', '1568236662314504193', '1', 'f379eaf3c831b04de153469d1bec345e');
INSERT INTO `mnjx_office` VALUES ('1569589820928299009', 'CTU123', '0', '1568236662314504193', '1', '75cfec2f18d5de09e7890842dd82777b');
INSERT INTO `mnjx_office` VALUES ('1570237341562355714', 'CTU001', '0', '1568236662314504193', '1', 'f379eaf3c831b04de153469d1bec345e');
INSERT INTO `mnjx_office` VALUES ('1570237419958091778', 'CTU002', '1', '1493493314010734594', '1', 'e10adc3949ba59abbe56e057f20f883e');
INSERT INTO `mnjx_office` VALUES ('1570289738384982018', 'CTU915', '0', '1568236662314504193', '1', 'f379eaf3c831b04de153469d1bec345e');
INSERT INTO `mnjx_office` VALUES ('1572780538951942146', 'SHA002', '0', '1572779833037996034', '1', 'f379eaf3c831b04de153469d1bec345e');
INSERT INTO `mnjx_office` VALUES ('1593526252880072705', 'TFU001', '1', '1503202256030273537', '1', 'f379eaf3c831b04de153469d1bec345e');
INSERT INTO `mnjx_office` VALUES ('1593550825088167937', 'PEK001', '0', '1568236662314504193', '1', 'f379eaf3c831b04de153469d1bec345e');
INSERT INTO `mnjx_office` VALUES ('1594527859535839233', 'SHA003', '0', '1568236662314504193', '1', 'f379eaf3c831b04de153469d1bec345e');
INSERT INTO `mnjx_office` VALUES ('1594603778249564162', 'CAN001', '0', '1572779833037996034', '1', 'f379eaf3c831b04de153469d1bec345e');
INSERT INTO `mnjx_office` VALUES ('1598241243658240001', 'PKX001', '1', '1503205097218248705', '1', 'f379eaf3c831b04de153469d1bec345e');
INSERT INTO `mnjx_office` VALUES ('1614833570489769986', 'SHA006', '0', '1568236662314504193', '1', 'f379eaf3c831b04de153469d1bec345e');
INSERT INTO `mnjx_office` VALUES ('1620962680874655745', 'SHA009', '0', '1620956246900400129', '1', 'f379eaf3c831b04de153469d1bec345e');
INSERT INTO `mnjx_office` VALUES ('1637732758580162561', 'SHA008', '0', '1637731867869376513', '1', 'f379eaf3c831b04de153469d1bec345e');

SET FOREIGN_KEY_CHECKS = 1;
