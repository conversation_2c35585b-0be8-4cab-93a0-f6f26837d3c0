#set(ticketDto = resDto[0].ticketDto)
NAME: #((ticketDto.adlName??)?ticketDto.adlName:ticketDto.xnName+" INF")         TKIN: #(format("{}-{}",subPre(ticketDto.ticketNo,3),subSuf(ticketDto.ticketNo,3)))#(wrap())
IATA OFFC: #(ticketDto.iataOffice)     ISSUED: #(ticketDto.issuedDate)        RVAL: 00#(wrap())
  1   #(subPre(ticketDto.issuedDate,5))/#(ticketDto.issuedTime)/#(ticketDto.issuedSiNo)   TRMK:    #(ticketDto.officeNo)+DEV-#(ticketDto.printNo)
