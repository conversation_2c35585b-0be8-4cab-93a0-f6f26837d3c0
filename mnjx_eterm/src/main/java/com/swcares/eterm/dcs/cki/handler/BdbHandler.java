package com.swcares.eterm.dcs.cki.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.obj.dto.BabDto;
import com.swcares.eterm.dcs.cki.obj.vo.BabRenderVo;
import com.swcares.eterm.dcs.cki.service.IBdbService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@OperateType(action = "BDB", template = "dcs/cki/bdb.jf", fullScreen = true)
public class BdbHandler implements Handler {

    @Resource
    private IBdbService iBdbService;

    @Override
    public Object handle(String cmd) throws UnifiedResultException {
        // 统一返回对象
        UnifiedResult unifiedResult = new UnifiedResult();
        BabDto babDto = iBdbService.parseBab(cmd);
        BabRenderVo babRenderVo = iBdbService.handle(babDto);
        unifiedResult.setArgs(new Object[]{cmd});
        unifiedResult.setResults(new Object[]{babRenderVo, babDto.getAcceptedNum(), babDto.getFailNum()});
        return unifiedResult;
    }
}
