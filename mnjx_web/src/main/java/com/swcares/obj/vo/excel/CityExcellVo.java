package com.swcares.obj.vo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class CityExcellVo implements Serializable {
    @ExcelProperty(value = "所属国家", order = 1)
    private String countryCname;
    @ExcelProperty(value = "城市中文名", order = 2)
    private String cityCname;
    @ExcelProperty(value = "城市英文名", order = 3)
    private String cityEname;
    @ExcelProperty(value = "城市代码", order = 4)
    private String cityCode;
    @ExcelProperty(value = "国际0/国内1", order = 5)
    private String cityType;
    @ExcelProperty(value = "城市状态（1有效0无效）", order = 6)
    private String cityStatus;
    @ExcelProperty(value = "备注", order = 7)
    private String remark;
}
