package com.swcares.eterm.crs.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
@OperateType(action = "SFN", template = "/crs/pfn.jf")
public class SfnHandler implements Handler {

    @Resource
    private PfnHandler pfnHandler;

    @Override
    public Object handle(String cmd) throws UnifiedResultException {
        return pfnHandler.handle(cmd);
    }
}
